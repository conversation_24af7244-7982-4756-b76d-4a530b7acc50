# ============================================================================
# MikroTik RouterOS Cloudflare DDNS 配置示例
# ============================================================================

# 这是一个配置示例文件，展示如何正确配置DDNS脚本
# 请复制此文件内容到主脚本的配置区域，并根据您的实际情况修改

# ============================================================================
# Cloudflare API配置
# ============================================================================

# API Token - 从Cloudflare控制台获取
# 权限要求: Zone:Zone:Read, Zone:DNS:Edit
:global cfApiToken "1234567890abcdef1234567890abcdef12345678"

# Cloudflare账户邮箱（用于日志记录，可选）
:global cfEmail "<EMAIL>"

# ============================================================================
# 域名配置
# ============================================================================

# 要管理的根域名列表（用逗号分隔，不要有空格）
# 示例：单个域名
:global cfDomains "example.com"

# 示例：多个域名
# :global cfDomains "example.com,mydomain.org,testsite.net"

# 要管理的子域名列表（用逗号分隔，不要有空格）
# "@" 或空字符串表示根域名记录
# 示例：常用子域名
:global cfSubdomains "@,www,mail,ftp,ssh,vpn"

# 示例：仅根域名和www
# :global cfSubdomains "@,www"

# 示例：包含API和服务子域名
# :global cfSubdomains "@,www,api,admin,blog,shop,mail,ftp"

# ============================================================================
# 网络接口配置
# ============================================================================

# WAN口接口名称 - 请根据您的实际配置修改
# 常见的接口名称：
:global wanInterface "ether1"        # 有线以太网接口
# :global wanInterface "pppoe-out1"  # PPPoE拨号接口
# :global wanInterface "lte1"        # LTE移动网络接口
# :global wanInterface "bridge1"     # 桥接接口

# ============================================================================
# 日志配置
# ============================================================================

# 是否启用日志记录
:global enableLogging true

# 日志级别设置
# "debug" - 显示所有调试信息（推荐用于故障排除）
# "info"  - 显示一般信息（推荐用于正常运行）
# "warning" - 仅显示警告和错误
# "error" - 仅显示错误信息
:global logLevel "info"

# ============================================================================
# IP检测配置
# ============================================================================

# IP检测服务列表（用逗号分隔）
# 脚本会依次尝试这些服务，直到成功获取IP
:global ipCheckServices "https://ipv4.icanhazip.com,https://api.ipify.org,https://checkip.amazonaws.com,https://ifconfig.me/ip"

# 是否强制更新DNS记录（忽略IP变化检测）
# true  - 每次运行都更新DNS记录
# false - 仅在IP地址发生变化时更新（推荐）
:global forceUpdate false

# ============================================================================
# 高级配置选项
# ============================================================================

# DNS记录TTL值（秒）
# 300  = 5分钟（推荐用于DDNS）
# 600  = 10分钟
# 3600 = 1小时
:global dnsTTL 300

# API请求超时时间（秒）
:global apiTimeout 30

# 重试次数（当API请求失败时）
:global maxRetries 3

# ============================================================================
# 配置验证提示
# ============================================================================

# 配置完成后，请确保：
# 1. cfApiToken 已设置为有效的Cloudflare API Token
# 2. cfDomains 包含您要管理的所有域名
# 3. cfSubdomains 包含您要管理的所有子域名
# 4. wanInterface 设置为正确的WAN接口名称
# 5. 所有域名都已在Cloudflare中托管

# 测试配置：
# 1. 在RouterOS终端中运行脚本
# 2. 检查系统日志中的DDNS相关消息
# 3. 验证DNS记录是否正确更新

# ============================================================================
# 常见配置示例
# ============================================================================

# 示例1：家庭用户配置
# :global cfDomains "myhome.com"
# :global cfSubdomains "@,www,nas,router"
# :global wanInterface "pppoe-out1"

# 示例2：小型企业配置
# :global cfDomains "company.com,backup-domain.org"
# :global cfSubdomains "@,www,mail,ftp,vpn,api,admin"
# :global wanInterface "ether1"

# 示例3：开发者配置
# :global cfDomains "dev-site.com"
# :global cfSubdomains "@,www,api,staging,test,demo,docs"
# :global wanInterface "ether1"
