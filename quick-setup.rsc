# ============================================================================
# MikroTik RouterOS Cloudflare DDNS 快速配置脚本
# 版本: 2.0
# 用途: 快速配置和部署DDNS脚本（适合有经验的用户）
# ============================================================================

# ============================================================================
# 快速配置区域 - 请修改以下配置后运行此脚本
# ============================================================================

# 必须配置的参数
:local cfApiToken "your_cloudflare_api_token_here"  # 替换为您的Cloudflare API Token
:local cfDomains "example.com"                      # 替换为您的域名
:local cfSubdomains "@,www"                         # 配置子域名列表
:local wanInterface "ether1"                        # WAN接口名称

# 可选配置参数
:local checkInterval "10m"                          # 检查间隔
:local logLevel "info"                              # 日志级别
:local enableScheduler true                         # 是否创建定时任务

# ============================================================================
# 验证配置
# ============================================================================

:if ($cfApiToken = "your_cloudflare_api_token_here") do={
    :put "错误: 请先配置Cloudflare API Token"
    :error "配置错误"
}

:if ($cfDomains = "example.com") do={
    :put "警告: 请确认域名配置是否正确"
}

# 检查接口是否存在
:local interfaceExists false
:foreach interface in=[/interface find] do={
    :if ([/interface get $interface name] = $wanInterface) do={
        :set interfaceExists true
    }
}
:if (!$interfaceExists) do={
    :put "错误: 接口 $wanInterface 不存在"
    :put "可用接口:"
    /interface print brief
    :error "接口配置错误"
}

:put "=== 快速配置 Cloudflare DDNS ==="
:put "域名: $cfDomains"
:put "子域名: $cfSubdomains"
:put "WAN接口: $wanInterface"
:put "检查间隔: $checkInterval"
:put ""

# ============================================================================
# 创建完整的DDNS脚本
# ============================================================================

:local ddnsScript "# MikroTik RouterOS Cloudflare DDNS Script
# Auto-generated by quick-setup.rsc

# Configuration
:global cfApiToken \"$cfApiToken\"
:global cfEmail \"<EMAIL>\"
:global cfDomains \"$cfDomains\"
:global cfSubdomains \"$cfSubdomains\"
:global wanInterface \"$wanInterface\"
:global enableLogging true
:global logLevel \"$logLevel\"
:global ipCheckServices \"https://ipv4.icanhazip.com,https://api.ipify.org,https://checkip.amazonaws.com\"
:global forceUpdate false

# Global variables
:global ddnsCurrentIP
:global ddnsLastIP
:global ddnsLogPrefix \"[DDNS]\"

# Logging function
:global ddnsLog do={
    :local level \$1
    :local message \$2
    
    :if (\$enableLogging) do={
        :local timestamp [/system clock get time]
        :local date [/system clock get date]
        :local logMsg \"\$ddnsLogPrefix [\$date \$timestamp] [\$level] \$message\"
        
        :if (\$level = \"error\") do={
            :log error \$logMsg
        } else={
            :if (\$level = \"warning\") do={
                :log warning \$logMsg
            } else={
                :log info \$logMsg
            }
        }
    }
}

# Get public IP function
:global ddnsGetPublicIP do={
    :local services [:toarray \$ipCheckServices]
    :local currentIP \"\"
    
    :foreach service in=\$services do={
        :do {
            \$ddnsLog \"info\" \"Getting public IP from \$service...\"
            :local result [/tool fetch url=\$service as-value output=user]
            :if (\$result->\"status\" = \"finished\") do={
                :set currentIP [:pick (\$result->\"data\") 0 [:find (\$result->\"data\") \"\\n\"]]
                :if ([:len \$currentIP] > 0) do={
                    \$ddnsLog \"info\" \"Got public IP: \$currentIP\"
                    :return \$currentIP
                }
            }
        } on-error={
            \$ddnsLog \"warning\" \"Failed to get IP from \$service\"
        }
    }
    
    \$ddnsLog \"error\" \"All IP detection services failed\"
    :return \"\"
}

# Get Cloudflare Zone ID
:global ddnsGetZoneID do={
    :local domain \$1
    :local url \"https://api.cloudflare.com/client/v4/zones?name=\$domain\"
    :local headers \"Authorization: Bearer \$cfApiToken,Content-Type: application/json\"
    
    :do {
        \$ddnsLog \"debug\" \"Getting Zone ID for \$domain...\"
        :local result [/tool fetch url=\$url http-header-field=\$headers as-value output=user]
        
        :if (\$result->\"status\" = \"finished\") do={
            :local response (\$result->\"data\")
            :local zoneIdStart [:find \$response \"\\\"id\\\":\\\"\"]
            :if (\$zoneIdStart >= 0) do={
                :set zoneIdStart (\$zoneIdStart + 6)
                :local zoneIdEnd [:find \$response \"\\\"\" \$zoneIdStart]
                :local zoneId [:pick \$response \$zoneIdStart \$zoneIdEnd]
                \$ddnsLog \"debug\" \"Zone ID for \$domain: \$zoneId\"
                :return \$zoneId
            }
        }
    } on-error={
        \$ddnsLog \"error\" \"Failed to get Zone ID for \$domain\"
    }
    
    :return \"\"
}

# Get DNS Record ID
:global ddnsGetRecordID do={
    :local zoneId \$1
    :local recordName \$2
    :local url \"https://api.cloudflare.com/client/v4/zones/\$zoneId/dns_records?name=\$recordName&type=A\"
    :local headers \"Authorization: Bearer \$cfApiToken,Content-Type: application/json\"
    
    :do {
        \$ddnsLog \"debug\" \"Getting Record ID for \$recordName...\"
        :local result [/tool fetch url=\$url http-header-field=\$headers as-value output=user]
        
        :if (\$result->\"status\" = \"finished\") do={
            :local response (\$result->\"data\")
            :local recordIdStart [:find \$response \"\\\"id\\\":\\\"\"]
            :if (\$recordIdStart >= 0) do={
                :set recordIdStart (\$recordIdStart + 6)
                :local recordIdEnd [:find \$response \"\\\"\" \$recordIdStart]
                :local recordId [:pick \$response \$recordIdStart \$recordIdEnd]
                \$ddnsLog \"debug\" \"Record ID for \$recordName: \$recordId\"
                :return \$recordId
            }
        }
    } on-error={
        \$ddnsLog \"error\" \"Failed to get Record ID for \$recordName\"
    }
    
    :return \"\"
}

# Update DNS Record
:global ddnsUpdateRecord do={
    :local zoneId \$1
    :local recordId \$2
    :local recordName \$3
    :local newIP \$4
    :local url \"https://api.cloudflare.com/client/v4/zones/\$zoneId/dns_records/\$recordId\"
    :local headers \"Authorization: Bearer \$cfApiToken,Content-Type: application/json\"
    :local data \"{\\\"type\\\":\\\"A\\\",\\\"name\\\":\\\"\$recordName\\\",\\\"content\\\":\\\"\$newIP\\\",\\\"ttl\\\":300}\"
    
    :do {
        \$ddnsLog \"info\" \"Updating DNS record \$recordName to \$newIP...\"
        :local result [/tool fetch url=\$url http-method=put http-header-field=\$headers http-data=\$data as-value output=user]
        
        :if (\$result->\"status\" = \"finished\") do={
            :local response (\$result->\"data\")
            :if ([:find \$response \"\\\"success\\\":true\"] >= 0) do={
                \$ddnsLog \"info\" \"DNS record \$recordName updated successfully\"
                :return true
            } else={
                \$ddnsLog \"error\" \"Failed to update DNS record \$recordName: \$response\"
                :return false
            }
        }
    } on-error={
        \$ddnsLog \"error\" \"Error updating DNS record \$recordName\"
    }
    
    :return false
}

# Create DNS Record
:global ddnsCreateRecord do={
    :local zoneId \$1
    :local recordName \$2
    :local newIP \$3
    :local url \"https://api.cloudflare.com/client/v4/zones/\$zoneId/dns_records\"
    :local headers \"Authorization: Bearer \$cfApiToken,Content-Type: application/json\"
    :local data \"{\\\"type\\\":\\\"A\\\",\\\"name\\\":\\\"\$recordName\\\",\\\"content\\\":\\\"\$newIP\\\",\\\"ttl\\\":300}\"
    
    :do {
        \$ddnsLog \"info\" \"Creating DNS record \$recordName...\"
        :local result [/tool fetch url=\$url http-method=post http-header-field=\$headers http-data=\$data as-value output=user]
        
        :if (\$result->\"status\" = \"finished\") do={
            :local response (\$result->\"data\")
            :if ([:find \$response \"\\\"success\\\":true\"] >= 0) do={
                \$ddnsLog \"info\" \"DNS record \$recordName created successfully\"
                :return true
            } else={
                \$ddnsLog \"error\" \"Failed to create DNS record \$recordName: \$response\"
                :return false
            }
        }
    } on-error={
        \$ddnsLog \"error\" \"Error creating DNS record \$recordName\"
    }
    
    :return false
}

# Process Domain
:global ddnsProcessDomain do={
    :local domain \$1
    :local newIP \$2
    :local subdomains [:toarray \$cfSubdomains]
    
    \$ddnsLog \"info\" \"Processing domain: \$domain\"
    
    :local zoneId [\$ddnsGetZoneID \$domain]
    :if ([:len \$zoneId] = 0) do={
        \$ddnsLog \"error\" \"Cannot get Zone ID for \$domain\"
        :return false
    }
    
    :local successCount 0
    :local totalCount [:len \$subdomains]
    
    :foreach subdomain in=\$subdomains do={
        :local recordName \"\"
        :if (\$subdomain = \"@\" or [:len \$subdomain] = 0) do={
            :set recordName \$domain
        } else={
            :set recordName \"\$subdomain.\$domain\"
        }
        
        :local recordId [\$ddnsGetRecordID \$zoneId \$recordName]
        
        :if ([:len \$recordId] > 0) do={
            :if ([\$ddnsUpdateRecord \$zoneId \$recordId \$recordName \$newIP]) do={
                :set successCount (\$successCount + 1)
            }
        } else={
            :if ([\$ddnsCreateRecord \$zoneId \$recordName \$newIP]) do={
                :set successCount (\$successCount + 1)
            }
        }
    }
    
    \$ddnsLog \"info\" \"Domain \$domain processed: \$successCount/\$totalCount records updated\"
    :return (\$successCount > 0)
}

# Main execution
\$ddnsLog \"info\" \"=== Cloudflare DDNS Script Started ===\"

:if ([:len \$cfApiToken] = 0 or \$cfApiToken = \"your_cloudflare_api_token_here\") do={
    \$ddnsLog \"error\" \"Please configure valid Cloudflare API Token\"
    :error \"Configuration error: Missing API Token\"
}

:if ([:len \$cfDomains] = 0) do={
    \$ddnsLog \"error\" \"Please configure domain list\"
    :error \"Configuration error: Missing domain configuration\"
}

:set ddnsCurrentIP [\$ddnsGetPublicIP]
:if ([:len \$ddnsCurrentIP] = 0) do={
    \$ddnsLog \"error\" \"Cannot get current public IP\"
    :error \"Network error: Cannot get public IP\"
}

:local needUpdate \$forceUpdate
:if (!\$needUpdate) do={
    :if ([:len \$ddnsLastIP] = 0 or \$ddnsCurrentIP != \$ddnsLastIP) do={
        :set needUpdate true
        \$ddnsLog \"info\" \"IP changed: \$ddnsLastIP -> \$ddnsCurrentIP\"
    } else={
        \$ddnsLog \"info\" \"IP unchanged: \$ddnsCurrentIP\"
    }
}

:if (\$needUpdate) do={
    :local domains [:toarray \$cfDomains]
    :local totalDomains [:len \$domains]
    :local successDomains 0
    
    \$ddnsLog \"info\" \"Updating DNS records for \$totalDomains domains...\"
    
    :foreach domain in=\$domains do={
        :if ([\$ddnsProcessDomain \$domain \$ddnsCurrentIP]) do={
            :set successDomains (\$successDomains + 1)
        }
    }
    
    :set ddnsLastIP \$ddnsCurrentIP
    
    \$ddnsLog \"info\" \"DNS update completed: \$successDomains/\$totalDomains domains updated\"
    
    :if (\$successDomains > 0) do={
        \$ddnsLog \"info\" \"=== DDNS Update Successful ===\"
    } else={
        \$ddnsLog \"error\" \"=== DDNS Update Failed ===\"
    }
} else={
    \$ddnsLog \"info\" \"=== DDNS Script Completed (No update needed) ===\"
}
"

# ============================================================================
# 安装脚本
# ============================================================================

:put "正在安装DDNS脚本..."

# 删除现有脚本
:do {
    /system script remove "cloudflare-ddns"
    :put "已删除现有脚本"
} on-error={}

# 创建新脚本
/system script add name="cloudflare-ddns" source=$ddnsScript
:put "✓ DDNS脚本安装成功"

# 创建定时任务
:if ($enableScheduler) do={
    :put "正在创建定时任务..."
    
    # 删除现有定时任务
    :do {
        /system scheduler remove "cloudflare-ddns"
    } on-error={}
    
    # 创建新定时任务
    /system scheduler add name="cloudflare-ddns" interval=$checkInterval on-event="/system script run cloudflare-ddns" start-time=startup
    :put "✓ 定时任务创建成功，间隔: $checkInterval"
}

# 测试脚本
:put ""
:put "正在测试脚本..."
:do {
    /system script run cloudflare-ddns
    :put "✓ 脚本测试成功"
} on-error={
    :put "❌ 脚本测试失败，请检查配置和日志"
}

:put ""
:put "=== 快速配置完成 ==="
:put "✓ DDNS脚本已安装并配置"
:if ($enableScheduler) do={
    :put "✓ 定时任务已创建"
}
:put ""
:put "管理命令:"
:put "  运行脚本: /system script run cloudflare-ddns"
:put "  查看日志: /log print where topics~\"script\""
:put "  查看定时任务: /system scheduler print"
:put ""
:put "如需故障排除，请运行: /import troubleshoot.rsc"
