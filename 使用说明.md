# MikroTik RouterOS Cloudflare DDNS 完整脚本使用说明

## 📋 脚本特性

这是一个**完整的、生产就绪的**MikroTik RouterOS Cloudflare DDNS脚本，具有以下特性：

- ✅ **完全自包含** - 无需额外依赖，可直接运行
- ✅ **本地IP获取** - 直接从WAN接口获取IP，无需外部服务
- ✅ **Cloudflare API v4** - 使用最新API规范和Token认证
- ✅ **多域名支持** - 同时管理多个根域名
- ✅ **多子域名支持** - 每个域名下支持多个子域名
- ✅ **智能检测** - 仅在IP变化时更新，减少API调用
- ✅ **多接口支持** - 支持PPPoE/DHCP/LTE/静态IP等各种连接方式
- ✅ **错误处理** - 完善的重试机制和错误处理
- ✅ **详细日志** - 带emoji的友好日志输出
- ✅ **配置验证** - 自动验证配置参数和接口状态
- ✅ **兼容性强** - 支持RouterOS 7.x及以上版本

## 🌐 IP获取方式

脚本采用**本地IP获取**方式，直接从路由器WAN接口读取公网IP地址，支持：

### 支持的连接类型
- **PPPoE拨号连接** - 自动从PPPoE客户端获取动态IP
- **DHCP客户端** - 从DHCP服务器获取的IP地址
- **静态IP配置** - 手动配置的静态公网IP
- **LTE移动网络** - 从LTE接口获取的移动网络IP
- **其他接口类型** - 桥接、VLAN等接口

### 智能IP识别
- **公网IP检测** - 自动识别并排除私有IP地址段
- **多方法获取** - 依次尝试多种方法获取IP地址
- **备用方案** - 可选启用外部IP检测服务作为备用

### IP获取流程
1. **接口IP地址检查** - 直接读取接口配置的IP地址
2. **PPPoE客户端检查** - 针对PPPoE连接的特殊处理
3. **LTE接口检查** - 针对移动网络的特殊处理
4. **DHCP客户端检查** - 检查DHCP获取的IP地址
5. **外部服务备用** - 可选的外部IP检测服务

### 私有IP地址过滤
脚本会自动排除以下私有IP地址段：
- `10.0.0.0/8` - 私有网络A类
- `**********/12` - 私有网络B类
- `***********/16` - 私有网络C类
- `***********/16` - APIPA自动配置
- `*********/8` - 本地回环地址

## 🚀 快速开始

### 第一步：获取Cloudflare API Token

1. 登录 [Cloudflare控制台](https://dash.cloudflare.com/)
2. 点击右上角头像 → "My Profile"
3. 选择 "API Tokens" 标签
4. 点击 "Create Token"
5. 选择 "Custom token" 模板
6. 配置权限：
   - **Zone** - `Zone:Read`
   - **Zone** - `DNS:Edit`
7. 配置区域资源：选择您要管理的域名
8. 点击 "Continue to summary" → "Create Token"
9. 复制生成的Token

### 第二步：配置脚本

打开 `mikrotik-cloudflare-ddns-complete.rsc` 文件，修改配置区域的参数：

```routeros
# 必须修改的配置
:global cfApiToken "your_cloudflare_api_token_here"  # 替换为您的API Token
:global cfDomains "example.com"                      # 替换为您的域名
:global cfSubdomains "@,www"                         # 配置子域名
:global wanInterface "ether1"                        # 设置WAN接口
```

#### 配置示例：

**单个域名配置：**
```routeros
:global cfApiToken "1234567890abcdef1234567890abcdef12345678"
:global cfDomains "mydomain.com"
:global cfSubdomains "@,www,mail,ftp"
:global wanInterface "ether1"
```

**多个域名配置：**
```routeros
:global cfApiToken "1234567890abcdef1234567890abcdef12345678"
:global cfDomains "mydomain.com,backup-domain.org"
:global cfSubdomains "@,www,api,admin"
:global wanInterface "pppoe-out1"
```

### 第三步：部署脚本

#### 方法1：通过WinBox（推荐）

1. 打开WinBox，连接到MikroTik设备
2. 点击 "Files" 菜单
3. 将脚本文件拖拽上传
4. 打开 "Terminal"，执行：
   ```routeros
   /import mikrotik-cloudflare-ddns-complete.rsc
   ```

#### 方法2：直接粘贴（适合小型配置）

1. 打开MikroTik终端或SSH连接
2. 直接复制粘贴整个脚本内容
3. 按回车执行

#### 方法3：通过Web界面

1. 登录MikroTik Web界面
2. 进入 "System" → "Scripts"
3. 点击 "Add New"
4. 设置脚本名称：`cloudflare-ddns`
5. 将脚本内容粘贴到编辑器中
6. 点击 "OK" 保存

### 第四步：测试脚本

在终端中运行以下命令测试脚本：

```routeros
/system script run cloudflare-ddns
```

如果配置正确，您将看到类似以下的输出：
```
ℹ️  🚀 === Cloudflare DDNS 脚本开始执行 ===
ℹ️  🔍 开始验证配置...
ℹ️  ✅ 配置验证通过
ℹ️  🌍 正在获取当前公网IP地址...
ℹ️  成功获取公网IP: 123.456.789.012
ℹ️  🌐 当前公网IP: 123.456.789.012
ℹ️  🆕 首次运行，将更新所有DNS记录
ℹ️  📝 开始更新 1 个域名的DNS记录...
ℹ️  🌐 开始处理域名: mydomain.com
ℹ️  ✅ DNS记录 mydomain.com 更新成功
ℹ️  ✅ DNS记录 www.mydomain.com 更新成功
ℹ️  📊 域名 mydomain.com 处理完成: 2/2 个记录成功更新
ℹ️  🎉 === 所有域名DNS记录更新成功 ===
ℹ️  🏁 脚本执行结束
```

### 第五步：设置定时任务

创建定时任务，让脚本自动运行：

```routeros
# 每10分钟检查一次（推荐）
/system scheduler add name="cloudflare-ddns" interval=10m on-event="/system script run cloudflare-ddns"

# 每5分钟检查一次（频繁）
/system scheduler add name="cloudflare-ddns" interval=5m on-event="/system script run cloudflare-ddns"

# 每30分钟检查一次（节约）
/system scheduler add name="cloudflare-ddns" interval=30m on-event="/system script run cloudflare-ddns"
```

## 🔧 配置参数详解

### 必须配置的参数

| 参数 | 说明 | 示例 |
|------|------|------|
| `cfApiToken` | Cloudflare API Token | `"1234567890abcdef..."` |
| `cfDomains` | 要管理的域名列表 | `"example.com,mydomain.org"` |
| `cfSubdomains` | 要管理的子域名列表 | `"@,www,mail,ftp,api"` |
| `wanInterface` | WAN接口名称 | `"ether1"` 或 `"pppoe-out1"` |

### 可选配置参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `enableLogging` | 是否启用日志记录 | `true` |
| `logLevel` | 日志级别 | `"info"` |
| `forceUpdate` | 是否强制更新 | `false` |
| `dnsTTL` | DNS记录TTL值（秒） | `300` |
| `maxRetries` | API请求重试次数 | `3` |
| `enableExternalIPCheck` | 是否启用外部IP检测备用方案 | `false` |
| `ipCheckServices` | 外部IP检测服务列表 | 多个服务 |

### 子域名配置说明

- `"@"` - 表示根域名记录（如 example.com）
- `"www"` - 表示www子域名（如 www.example.com）
- `"mail"` - 表示邮件子域名（如 mail.example.com）

### WAN接口配置说明

#### 接口类型和命名规则

**以太网接口：**
```routeros
:global wanInterface "ether1"    # 第一个以太网端口
:global wanInterface "ether2"    # 第二个以太网端口
```

**PPPoE拨号接口：**
```routeros
:global wanInterface "pppoe-out1"    # PPPoE客户端接口
:global wanInterface "pppoe-wan"     # 自定义名称的PPPoE接口
```

**LTE移动网络接口：**
```routeros
:global wanInterface "lte1"      # LTE接口
:global wanInterface "lte-wan"   # 自定义名称的LTE接口
```

**桥接接口：**
```routeros
:global wanInterface "bridge1"   # 桥接接口
:global wanInterface "bridge-wan" # 自定义名称的桥接接口
```

#### 查看可用接口

使用以下命令查看系统中的所有接口：
```routeros
/interface print
```

查看接口的IP地址配置：
```routeros
/ip address print
```

查看DHCP客户端状态：
```routeros
/ip dhcp-client print
```

查看PPPoE客户端状态：
```routeros
/interface pppoe-client print
```

## 📊 管理命令

```routeros
# 手动运行脚本
/system script run cloudflare-ddns

# 查看脚本列表
/system script print

# 查看系统日志
/log print where topics~"script"

# 查看最近的日志
/log print where time>([/system clock get time] - 00:30:00)

# 查看定时任务
/system scheduler print

# 启用定时任务
/system scheduler enable cloudflare-ddns

# 禁用定时任务
/system scheduler disable cloudflare-ddns

# 删除定时任务
/system scheduler remove cloudflare-ddns
```

## 🔍 故障排除

### 常见错误及解决方法

#### 1. "请配置有效的Cloudflare API Token"
- **原因**：API Token未配置或配置错误
- **解决**：检查Token是否正确复制，确认权限设置

#### 2. "WAN接口不存在"
- **原因**：接口名称配置错误
- **解决**：运行 `/interface print` 查看可用接口名称

#### 3. "无法获取公网IP地址"
- **原因**：WAN接口未配置IP或配置错误
- **解决**：
  - 检查接口状态：`/interface print where name=your-wan-interface`
  - 检查IP配置：`/ip address print where interface=your-wan-interface`
  - 检查DHCP客户端：`/ip dhcp-client print`
  - 检查PPPoE状态：`/interface pppoe-client print`

#### 4. "接口IP是私有地址"
- **原因**：WAN接口获取到的是私有IP地址（如192.168.x.x）
- **解决**：
  - 检查网络拓扑，确认路由器直接连接到公网
  - 如果在NAT后面，考虑启用外部IP检测：`enableExternalIPCheck = true`
  - 检查上级路由器的配置

#### 4. "无法获取域名的Zone ID"
- **原因**：域名未在Cloudflare托管或API权限不足
- **解决**：确认域名在Cloudflare中，检查API Token权限

### 调试方法

1. **启用调试日志**：
   ```routeros
   :global logLevel "debug"
   ```

2. **查看详细日志**：
   ```routeros
   /log print where topics~"script"
   ```

3. **检查WAN接口状态**：
   ```routeros
   # 查看接口状态
   /interface print where name=your-wan-interface

   # 查看接口IP地址
   /ip address print where interface=your-wan-interface

   # 查看DHCP客户端状态
   /ip dhcp-client print where interface=your-wan-interface

   # 查看PPPoE客户端状态
   /interface pppoe-client print where name=your-wan-interface
   ```

4. **手动测试IP获取**：
   ```routeros
   # 如果启用了外部IP检测备用方案
   /tool fetch url="https://api.ipify.org" dst-path=test-ip.txt
   /file get test-ip.txt contents
   ```

5. **测试Cloudflare API连接**：
   ```routeros
   /tool fetch url="https://api.cloudflare.com/client/v4/" dst-path=test-api.txt
   /file get test-api.txt contents
   ```

## 🔒 安全建议

1. **API Token权限最小化** - 仅授予必要的Zone:Read和DNS:Edit权限
2. **定期更新Token** - 建议每6个月更换一次API Token
3. **监控日志** - 定期检查脚本运行日志
4. **备份配置** - 保存脚本配置的备份

## 📝 版本信息

- **版本**：2.0 - 生产就绪版本
- **兼容性**：RouterOS 7.x 及以上版本
- **API版本**：Cloudflare API v4
- **认证方式**：API Token

## 💡 使用技巧

1. **首次运行**：建议先手动运行一次，确认配置正确
2. **日志监控**：定期查看日志，确保脚本正常运行
3. **备用域名**：可以配置多个域名作为备份
4. **合理间隔**：建议设置10-30分钟的检查间隔

---

**注意**：请确保您的域名已在Cloudflare中正确托管，并且MikroTik设备有稳定的互联网连接。
