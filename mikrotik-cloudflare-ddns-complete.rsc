# ============================================================================
# MikroTik RouterOS Cloudflare DDNS 完整脚本
# 版本: 2.1 - 生产就绪版本 (本地IP获取)
# 兼容: RouterOS 7.x 及以上版本
# API: Cloudflare API v4 with Token Authentication
# IP获取: 直接从WAN接口获取，支持PPPoE/DHCP/LTE/静态IP
# ============================================================================

# ============================================================================
# 配置区域 - 请根据您的实际情况修改以下配置
# ============================================================================

# Cloudflare API Token (必须配置)
# 获取方式: https://dash.cloudflare.com/profile/api-tokens
# 权限要求: Zone:Zone:Read, Zone:DNS:Edit
:global cfApiToken "your_cloudflare_api_token_here"

# 要管理的域名列表 (必须配置)
# 格式: "域名1,域名2,域名3" (用逗号分隔，不要有空格)
# 示例: "example.com" 或 "example.com,mydomain.org"
:global cfDomains "example.com"

# 要管理的子域名列表 (必须配置)
# 格式: "子域名1,子域名2,子域名3" (用逗号分隔，不要有空格)
# "@" 表示根域名记录，留空或"@"都表示根域名
# 示例: "@,www" 或 "@,www,mail,ftp,api"
:global cfSubdomains "@,www"

# WAN接口名称 (必须配置)
# 常见值: "ether1", "pppoe-out1", "lte1", "bridge1"
# 查看可用接口: /interface print
:global wanInterface "internet"

# 日志配置 (可选配置)
:global enableLogging true                    # 是否启用日志记录
:global logLevel "info"                       # 日志级别: debug, info, warning, error

# IP获取配置 (可选配置)
# 是否启用外部IP检测服务作为备用方案（当无法从接口获取IP时）
:global enableExternalIPCheck false
:global ipCheckServices "http://myip.ipip.net,http://ip.3322.net,http://members.3322.org/dyndns/getip"

# 高级选项 (可选配置)
:global forceUpdate false                     # 是否强制更新(忽略IP变化检测)
:global dnsTTL 300                           # DNS记录TTL值(秒)
:global maxRetries 3                         # API请求失败时的重试次数
:global useProxy false                       # 是否使用HTTP代理
:global proxyServer ""                       # 代理服务器地址 (格式: IP:端口)

# ============================================================================
# 全局变量定义 (请勿修改)
# ============================================================================

:global ddnsCurrentIP
:global ddnsLastIP
:global ddnsLogPrefix "[CLOUDFLARE-DDNS]"

# ============================================================================
# 日志记录函数 (请勿修改)
# ============================================================================

:global ddnsLog do={
    :local level $1
    :local message $2
    
    :if ($enableLogging) do={
        :local timestamp [/system clock get time]
        :local date [/system clock get date]
        :local logMsg "$ddnsLogPrefix [$date $timestamp] [$level] $message"
        
        :if ($level = "error") do={
            :log error $logMsg
        } else={
            :if ($level = "warning") do={
                :log warning $logMsg
            } else={
                :log info $logMsg
            }
        }
        
        # 同时输出到终端(如果是手动运行)
        :if ($level = "error") do={
            :put "❌ $message"
        } else={
            :if ($level = "warning") do={
                :put "⚠️  $message"
            } else={
                :if ($level = "info") do={
                    :put "ℹ️  $message"
                } else={
                    :put "🔍 $message"
                }
            }
        }
    }
}

# ============================================================================
# 获取公网IP地址函数 (请勿修改)
# ============================================================================

:global ddnsGetPublicIP do={
    :local currentIP ""

    $ddnsLog "debug" "正在从WAN接口 $wanInterface 获取公网IP地址..."

    # 方法1: 直接从接口IP地址获取
    :do {
        :local addresses [/ip address find where interface=$wanInterface and !invalid]
        :if ([:len $addresses] > 0) do={
            :foreach addr in=$addresses do={
                :local address [/ip address get $addr address]
                :local network [/ip address get $addr network]
                :set currentIP [:pick $address 0 [:find $address "/"]]

                $ddnsLog "debug" "接口 $wanInterface 地址: $currentIP (网络: $network)"

                # 验证是否为公网IP（排除私有IP段）
                :if ([:len $currentIP] > 7) do={
                    :local isPublic true
                    :local firstOctet [:tonum [:pick $currentIP 0 [:find $currentIP "."]]]
                    :local secondOctet 0

                    # 提取第二个八位组用于更精确的判断
                    :local firstDot [:find $currentIP "."]
                    :if ($firstDot >= 0) do={
                        :local secondDot [:find $currentIP "." ($firstDot + 1)]
                        :if ($secondDot >= 0) do={
                            :set secondOctet [:tonum [:pick $currentIP ($firstDot + 1) $secondDot]]
                        }
                    }

                    # 检查私有IP段
                    # 10.0.0.0/8
                    :if ($firstOctet = 10) do={ :set isPublic false }
                    # **********/12
                    :if ($firstOctet = 172 and $secondOctet >= 16 and $secondOctet <= 31) do={ :set isPublic false }
                    # ***********/16
                    :if ($firstOctet = 192 and $secondOctet = 168) do={ :set isPublic false }
                    # ***********/16 (APIPA)
                    :if ($firstOctet = 169 and $secondOctet = 254) do={ :set isPublic false }
                    # *********/8 (Loopback)
                    :if ($firstOctet = 127) do={ :set isPublic false }

                    :if ($isPublic) do={
                        $ddnsLog "info" "✅ 从接口获取到公网IP: $currentIP"
                        :return $currentIP
                    } else={
                        $ddnsLog "debug" "接口IP $currentIP 是私有地址，继续查找..."
                    }
                }
            }
        }
    } on-error={
        $ddnsLog "debug" "从接口地址获取IP失败"
    }

    # 方法2: 从PPPoE客户端获取IP（适用于PPPoE连接）
    :if ([:find $wanInterface "pppoe"] >= 0 or $wanInterface = "internet") do={
        :do {
            # 直接从PPPoE接口获取动态IP地址
            :local addresses [/ip address find where interface=$wanInterface and dynamic]
            :if ([:len $addresses] > 0) do={
                :local address [/ip address get [:pick $addresses 0] address]
                :set currentIP [:pick $address 0 [:find $address "/"]]

                # 验证IP是否为公网地址
                :if ([:len $currentIP] > 7) do={
                    :local firstOctet [:tonum [:pick $currentIP 0 [:find $currentIP "."]]]
                    # 检查是否为公网IP（排除私有IP段）
                    :if ($firstOctet != 10 and $firstOctet != 172 and $firstOctet != 192 and $firstOctet != 127) do={
                        $ddnsLog "info" "✅ 从PPPoE接口获取到公网IP: $currentIP"
                        :return $currentIP
                    } else={
                        $ddnsLog "debug" "PPPoE接口IP $currentIP 是私有地址"
                    }
                }
            } else={
                $ddnsLog "debug" "PPPoE接口 $wanInterface 没有动态IP地址"
            }
        } on-error={
            $ddnsLog "debug" "从PPPoE接口获取IP失败"
        }
    }

    # 方法3: 从LTE接口获取IP（适用于移动网络）
    :if ([:find $wanInterface "lte"] >= 0) do={
        :do {
            :local lteInterface [/interface lte find where name=$wanInterface]
            :if ([:len $lteInterface] > 0) do={
                :local status [/interface lte get $lteInterface running]
                :if ($status) do={
                    # 获取LTE接口的动态IP
                    :local addresses [/ip address find where interface=$wanInterface]
                    :if ([:len $addresses] > 0) do={
                        :local address [/ip address get [:pick $addresses 0] address]
                        :set currentIP [:pick $address 0 [:find $address "/"]]
                        $ddnsLog "info" "✅ 从LTE接口获取到IP: $currentIP"
                        :return $currentIP
                    }
                }
            }
        } on-error={
            $ddnsLog "debug" "从LTE接口获取IP失败"
        }
    }

    # 方法4: 从DHCP客户端获取IP（适用于DHCP连接）
    :do {
        :local dhcpClient [/ip dhcp-client find where interface=$wanInterface]
        :if ([:len $dhcpClient] > 0) do={
            :local status [/ip dhcp-client get $dhcpClient status]
            :local address [/ip dhcp-client get $dhcpClient address]
            :if ($status = "bound" and [:len $address] > 0) do={
                :set currentIP $address
                $ddnsLog "info" "✅ 从DHCP客户端获取到IP: $currentIP"
                :return $currentIP
            }
        }
    } on-error={
        $ddnsLog "debug" "从DHCP客户端获取IP失败"
    }

    # 方法5: 使用Cloudflare trace端点获取IP（推荐用于中国大陆）
    :do {
        $ddnsLog "debug" "正在从Cloudflare trace端点获取公网IP..."
        :local result [/tool fetch url="https://cloudflare.com/cdn-cgi/trace" as-value output=user]
        :if ($result->"status" = "finished") do={
            :local response ($result->"data")
            # 从trace响应中提取IP地址
            :local ipStart [:find $response "ip="]
            :if ($ipStart >= 0) do={
                :set ipStart ($ipStart + 3)
                :local ipEnd [:find $response "\n" $ipStart]
                :if ($ipEnd < 0) do={ :set ipEnd [:len $response] }
                :set currentIP [:pick $response $ipStart $ipEnd]
                :if ([:len $currentIP] > 7 and [:len $currentIP] < 16) do={
                    :if ([:find $currentIP "."] > 0) do={
                        $ddnsLog "info" "✅ 从Cloudflare trace获取到公网IP: $currentIP"
                        :return $currentIP
                    }
                }
            }
        }
    } on-error={
        $ddnsLog "debug" "从Cloudflare trace获取IP失败"
    }

    # 方法6: 备用方案 - 使用其他IP检测服务（如果启用）
    :if ($enableExternalIPCheck) do={
        $ddnsLog "warning" "⚠️  无法从接口和trace获取IP，尝试使用其他检测服务..."
        :local services [:toarray $ipCheckServices]

        :foreach service in=$services do={
            :do {
                $ddnsLog "debug" "正在从 $service 获取公网IP地址..."
                :local result [/tool fetch url=$service as-value output=user]
                :if ($result->"status" = "finished") do={
                    :set currentIP [:pick ($result->"data") 0 [:find ($result->"data") "\n"]]
                    # 清理可能的回车符和空格
                    :set currentIP [:tostr $currentIP]
                    :if ([:len $currentIP] > 7 and [:len $currentIP] < 16) do={
                        # 简单验证IP格式
                        :if ([:find $currentIP "."] > 0) do={
                            $ddnsLog "info" "✅ 从外部服务获取到公网IP: $currentIP"
                            :return $currentIP
                        }
                    }
                }
            } on-error={
                $ddnsLog "debug" "从 $service 获取IP失败，尝试下一个服务..."
            }
        }
    }

    $ddnsLog "error" "❌ 无法获取公网IP地址，请检查WAN接口配置和网络连接"
    $ddnsLog "info" "💡 提示: 确保接口 '$wanInterface' 已正确配置并获得公网IP地址"
    :return ""
}

# ============================================================================
# 获取Cloudflare Zone ID函数 (请勿修改)
# ============================================================================

:global ddnsGetZoneID do={
    :local domain $1
    :local url "https://api.cloudflare.com/client/v4/zones?name=$domain"
    :local headers "User-Agent: MikroTik-DDNS/2.1,Authorization: Bearer $cfApiToken,Content-Type: application/json,Accept: application/json"
    
    :local retryCount 0
    :while ($retryCount < $maxRetries) do={
        :do {
            $ddnsLog "debug" "正在获取域名 $domain 的Zone ID (尝试 $($retryCount + 1)/$maxRetries)..."
            :local result [/tool fetch url=$url http-header-field=$headers as-value output=user]
            
            :if ($result->"status" = "finished") do={
                :local response ($result->"data")
                
                # 检查API响应是否成功
                :if ([:find $response "\"success\":true"] >= 0) do={
                    # 提取Zone ID
                    :local zoneIdStart [:find $response "\"id\":\""]
                    :if ($zoneIdStart >= 0) do={
                        :set zoneIdStart ($zoneIdStart + 6)
                        :local zoneIdEnd [:find $response "\"" $zoneIdStart]
                        :local zoneId [:pick $response $zoneIdStart $zoneIdEnd]
                        $ddnsLog "debug" "域名 $domain 的Zone ID: $zoneId"
                        :return $zoneId
                    }
                } else={
                    $ddnsLog "error" "Cloudflare API返回错误: $response"
                }
            }
        } on-error={
            $ddnsLog "warning" "获取Zone ID失败 (尝试 $($retryCount + 1)/$maxRetries)"
        }
        :set retryCount ($retryCount + 1)
        :if ($retryCount < $maxRetries) do={
            :delay 2s
        }
    }
    
    $ddnsLog "error" "无法获取域名 $domain 的Zone ID，请检查域名是否在Cloudflare中托管"
    :return ""
}

# ============================================================================
# 获取DNS记录ID函数 (请勿修改)
# ============================================================================

:global ddnsGetRecordID do={
    :local zoneId $1
    :local recordName $2
    :local url "https://api.cloudflare.com/client/v4/zones/$zoneId/dns_records?name=$recordName&type=A"
    :local headers "User-Agent: MikroTik-DDNS/2.1,Authorization: Bearer $cfApiToken,Content-Type: application/json,Accept: application/json"

    :local retryCount 0
    :while ($retryCount < $maxRetries) do={
        :do {
            $ddnsLog "debug" "正在获取记录 $recordName 的Record ID (尝试 $($retryCount + 1)/$maxRetries)..."
            :local result [/tool fetch url=$url http-header-field=$headers as-value output=user]

            :if ($result->"status" = "finished") do={
                :local response ($result->"data")

                # 检查API响应是否成功
                :if ([:find $response "\"success\":true"] >= 0) do={
                    # 提取Record ID
                    :local recordIdStart [:find $response "\"id\":\""]
                    :if ($recordIdStart >= 0) do={
                        :set recordIdStart ($recordIdStart + 6)
                        :local recordIdEnd [:find $response "\"" $recordIdStart]
                        :local recordId [:pick $response $recordIdStart $recordIdEnd]
                        $ddnsLog "debug" "记录 $recordName 的Record ID: $recordId"
                        :return $recordId
                    } else={
                        $ddnsLog "debug" "记录 $recordName 不存在，将创建新记录"
                        :return ""
                    }
                } else={
                    $ddnsLog "error" "获取DNS记录时API返回错误: $response"
                }
            }
        } on-error={
            $ddnsLog "warning" "获取Record ID失败 (尝试 $($retryCount + 1)/$maxRetries)"
        }
        :set retryCount ($retryCount + 1)
        :if ($retryCount < $maxRetries) do={
            :delay 2s
        }
    }

    $ddnsLog "warning" "无法获取记录 $recordName 的Record ID，将尝试创建新记录"
    :return ""
}

# ============================================================================
# 更新DNS记录函数 (请勿修改)
# ============================================================================

:global ddnsUpdateRecord do={
    :local zoneId $1
    :local recordId $2
    :local recordName $3
    :local newIP $4
    :local url "https://api.cloudflare.com/client/v4/zones/$zoneId/dns_records/$recordId"
    :local headers "User-Agent: MikroTik-DDNS/2.1,Authorization: Bearer $cfApiToken,Content-Type: application/json,Accept: application/json"
    :local data "{\"type\":\"A\",\"name\":\"$recordName\",\"content\":\"$newIP\",\"ttl\":$dnsTTL}"

    :local retryCount 0
    :while ($retryCount < $maxRetries) do={
        :do {
            $ddnsLog "info" "正在更新DNS记录 $recordName 到 $newIP (尝试 $($retryCount + 1)/$maxRetries)..."
            :local result [/tool fetch url=$url http-method=put http-header-field=$headers http-data=$data as-value output=user]

            :if ($result->"status" = "finished") do={
                :local response ($result->"data")

                # 检查更新是否成功
                :if ([:find $response "\"success\":true"] >= 0) do={
                    $ddnsLog "info" "✅ DNS记录 $recordName 更新成功"
                    :return true
                } else={
                    $ddnsLog "error" "DNS记录 $recordName 更新失败: $response"
                }
            }
        } on-error={
            $ddnsLog "warning" "更新DNS记录失败 (尝试 $($retryCount + 1)/$maxRetries)"
        }
        :set retryCount ($retryCount + 1)
        :if ($retryCount < $maxRetries) do={
            :delay 2s
        }
    }

    $ddnsLog "error" "❌ 更新DNS记录 $recordName 最终失败"
    :return false
}

# ============================================================================
# 创建DNS记录函数 (请勿修改)
# ============================================================================

:global ddnsCreateRecord do={
    :local zoneId $1
    :local recordName $2
    :local newIP $3
    :local url "https://api.cloudflare.com/client/v4/zones/$zoneId/dns_records"
    :local headers "Authorization: Bearer $cfApiToken,Content-Type: application/json"
    :local data "{\"type\":\"A\",\"name\":\"$recordName\",\"content\":\"$newIP\",\"ttl\":$dnsTTL}"

    :local retryCount 0
    :while ($retryCount < $maxRetries) do={
        :do {
            $ddnsLog "info" "正在创建DNS记录 $recordName (尝试 $($retryCount + 1)/$maxRetries)..."
            :local result [/tool fetch url=$url http-method=post http-header-field=$headers http-data=$data as-value output=user]

            :if ($result->"status" = "finished") do={
                :local response ($result->"data")

                # 检查创建是否成功
                :if ([:find $response "\"success\":true"] >= 0) do={
                    $ddnsLog "info" "✅ DNS记录 $recordName 创建成功"
                    :return true
                } else={
                    $ddnsLog "error" "DNS记录 $recordName 创建失败: $response"
                }
            }
        } on-error={
            $ddnsLog "warning" "创建DNS记录失败 (尝试 $($retryCount + 1)/$maxRetries)"
        }
        :set retryCount ($retryCount + 1)
        :if ($retryCount < $maxRetries) do={
            :delay 2s
        }
    }

    $ddnsLog "error" "❌ 创建DNS记录 $recordName 最终失败"
    :return false
}

# ============================================================================
# 处理单个域名的所有子域名函数 (请勿修改)
# ============================================================================

:global ddnsProcessDomain do={
    :local domain $1
    :local newIP $2
    :local subdomains [:toarray $cfSubdomains]

    $ddnsLog "info" "🌐 开始处理域名: $domain"

    # 获取Zone ID
    :local zoneId [$ddnsGetZoneID $domain]
    :if ([:len $zoneId] = 0) do={
        $ddnsLog "error" "❌ 无法获取域名 $domain 的Zone ID，跳过此域名"
        :return false
    }

    :local successCount 0
    :local totalCount [:len $subdomains]

    # 处理每个子域名
    :foreach subdomain in=$subdomains do={
        :local recordName ""
        :if ($subdomain = "@" or [:len $subdomain] = 0) do={
            :set recordName $domain
            $ddnsLog "debug" "处理根域名记录: $recordName"
        } else={
            :set recordName "$subdomain.$domain"
            $ddnsLog "debug" "处理子域名记录: $recordName"
        }

        # 获取记录ID
        :local recordId [$ddnsGetRecordID $zoneId $recordName]

        :if ([:len $recordId] > 0) do={
            # 记录存在，更新它
            :if ([$ddnsUpdateRecord $zoneId $recordId $recordName $newIP]) do={
                :set successCount ($successCount + 1)
            }
        } else={
            # 记录不存在，创建它
            :if ([$ddnsCreateRecord $zoneId $recordName $newIP]) do={
                :set successCount ($successCount + 1)
            }
        }

        # 在处理记录之间添加短暂延迟，避免API限制
        :delay 1s
    }

    $ddnsLog "info" "📊 域名 $domain 处理完成: $successCount/$totalCount 个记录成功更新"
    :return ($successCount > 0)
}

# ============================================================================
# 配置验证函数 (请勿修改)
# ============================================================================

:global ddnsValidateConfig do={
    $ddnsLog "info" "🔍 开始验证配置..."

    # 验证API Token
    :if ([:len $cfApiToken] = 0 or $cfApiToken = "your_cloudflare_api_token_here") do={
        $ddnsLog "error" "❌ 请配置有效的Cloudflare API Token"
        $ddnsLog "info" "获取Token: https://dash.cloudflare.com/profile/api-tokens"
        :return false
    }

    # 验证域名配置
    :if ([:len $cfDomains] = 0 or $cfDomains = "example.com") do={
        $ddnsLog "error" "❌ 请配置要管理的域名列表"
        :return false
    }

    # 验证子域名配置
    :if ([:len $cfSubdomains] = 0) do={
        $ddnsLog "error" "❌ 请配置要管理的子域名列表"
        :return false
    }

    # 验证WAN接口
    :local interfaceExists false
    :local interfaceRunning false
    :foreach interface in=[/interface find] do={
        :if ([/interface get $interface name] = $wanInterface) do={
            :set interfaceExists true
            :local running [/interface get $interface running]
            :local disabled [/interface get $interface disabled]
            :if ($running and !$disabled) do={
                :set interfaceRunning true
                $ddnsLog "debug" "接口 $wanInterface 状态: 运行中"
            } else={
                $ddnsLog "warning" "⚠️  接口 $wanInterface 状态: " . [:if $disabled then "已禁用" else "未运行"]
            }
        }
    }
    :if (!$interfaceExists) do={
        $ddnsLog "error" "❌ WAN接口 '$wanInterface' 不存在"
        $ddnsLog "info" "可用接口列表:"
        /interface print brief where !disabled
        :return false
    }
    :if (!$interfaceRunning) do={
        $ddnsLog "warning" "⚠️  WAN接口 '$wanInterface' 未运行或已禁用，可能影响IP获取"
    }

    # 检查接口是否有IP地址
    :local hasIPAddress false
    :do {
        :local addresses [/ip address find where interface=$wanInterface and !invalid]
        :if ([:len $addresses] > 0) do={
            :set hasIPAddress true
            $ddnsLog "debug" "接口 $wanInterface 已配置IP地址"
        } else={
            # 检查是否为动态获取IP的接口类型
            :if ([:find $wanInterface "pppoe"] >= 0 or [:find $wanInterface "lte"] >= 0) do={
                $ddnsLog "debug" "接口 $wanInterface 为动态IP接口，将在运行时获取地址"
                :set hasIPAddress true
            } else={
                # 检查DHCP客户端
                :local dhcpClient [/ip dhcp-client find where interface=$wanInterface]
                :if ([:len $dhcpClient] > 0) do={
                    $ddnsLog "debug" "接口 $wanInterface 配置了DHCP客户端"
                    :set hasIPAddress true
                }
            }
        }
    } on-error={
        $ddnsLog "debug" "检查接口IP地址时出错"
    }

    :if (!$hasIPAddress) do={
        $ddnsLog "warning" "⚠️  接口 '$wanInterface' 似乎没有配置IP地址，请检查网络配置"
    }

    $ddnsLog "info" "✅ 配置验证通过"
    :return true
}

# ============================================================================
# 主执行逻辑开始 (请勿修改)
# ============================================================================

$ddnsLog "info" "🚀 === Cloudflare DDNS 脚本开始执行 ==="
$ddnsLog "info" "📅 执行时间: [/system clock get date] [/system clock get time]"

# 验证配置
:if (![$ddnsValidateConfig]) do={
    $ddnsLog "error" "❌ 配置验证失败，脚本终止"
    $ddnsLog "info" "请检查脚本顶部的配置区域并修改相应参数"
    :error "配置错误"
}

# 显示当前配置信息
$ddnsLog "info" "📋 当前配置:"
$ddnsLog "info" "   - 域名: $cfDomains"
$ddnsLog "info" "   - 子域名: $cfSubdomains"
$ddnsLog "info" "   - WAN接口: $wanInterface"
$ddnsLog "info" "   - 强制更新: $forceUpdate"

# 获取当前公网IP
$ddnsLog "info" "🌍 正在获取当前公网IP地址..."
:set ddnsCurrentIP [$ddnsGetPublicIP]
:if ([:len $ddnsCurrentIP] = 0) do={
    $ddnsLog "error" "❌ 无法获取当前公网IP地址，脚本终止"
    $ddnsLog "info" "请检查网络连接和防火墙设置"
    :error "网络错误：无法获取公网IP"
}

$ddnsLog "info" "🌐 当前公网IP: $ddnsCurrentIP"

# 检查IP是否发生变化
:local needUpdate $forceUpdate
:if (!$needUpdate) do={
    :if ([:len $ddnsLastIP] = 0) do={
        :set needUpdate true
        $ddnsLog "info" "🆕 首次运行，将更新所有DNS记录"
    } else={
        :if ($ddnsCurrentIP != $ddnsLastIP) do={
            :set needUpdate true
            $ddnsLog "info" "🔄 检测到IP地址变化: $ddnsLastIP → $ddnsCurrentIP"
        } else={
            $ddnsLog "info" "✅ IP地址未发生变化: $ddnsCurrentIP，跳过更新"
        }
    }
} else={
    $ddnsLog "info" "🔄 强制更新模式已启用"
}

# 如果需要更新DNS记录
:if ($needUpdate) do={
    :local domains [:toarray $cfDomains]
    :local totalDomains [:len $domains]
    :local successDomains 0

    $ddnsLog "info" "📝 开始更新 $totalDomains 个域名的DNS记录..."

    # 处理每个域名
    :foreach domain in=$domains do={
        :if ([$ddnsProcessDomain $domain $ddnsCurrentIP]) do={
            :set successDomains ($successDomains + 1)
        }

        # 在处理域名之间添加延迟，避免API限制
        :if ($totalDomains > 1) do={
            :delay 2s
        }
    }

    # 更新记录的IP地址
    :set ddnsLastIP $ddnsCurrentIP

    # 输出最终结果
    $ddnsLog "info" "📊 DNS更新统计: $successDomains/$totalDomains 个域名成功更新"

    :if ($successDomains > 0) do={
        :if ($successDomains = $totalDomains) do={
            $ddnsLog "info" "🎉 === 所有域名DNS记录更新成功 ==="
        } else={
            $ddnsLog "warning" "⚠️  === 部分域名DNS记录更新成功 ==="
        }
    } else={
        $ddnsLog "error" "❌ === 所有域名DNS记录更新失败 ==="
    }
} else={
    $ddnsLog "info" "✅ === DDNS脚本执行完成（无需更新）==="
}

$ddnsLog "info" "🏁 脚本执行结束"

# ============================================================================
# 使用说明和管理命令
# ============================================================================

# 脚本使用说明:
# 1. 修改脚本顶部配置区域的参数
# 2. 将脚本导入到MikroTik设备: /import script-name.rsc
# 3. 或直接在终端中粘贴运行
# 4. 设置定时任务: /system scheduler add name="cloudflare-ddns" interval=10m on-event="/system script run cloudflare-ddns"
#
# IP获取方式说明:
# - 脚本直接从指定的WAN接口获取公网IP地址
# - 支持PPPoE拨号、DHCP客户端、LTE移动网络、静态IP配置
# - 自动识别并排除私有IP地址段
# - 可选启用外部IP检测服务作为备用方案
#
# 支持的接口类型:
# - 以太网接口 (ether1, ether2, ...)
# - PPPoE接口 (pppoe-out1, pppoe-out2, ...)
# - LTE接口 (lte1, lte2, ...)
# - 桥接接口 (bridge1, bridge2, ...)
# - DHCP客户端接口
#
# 管理命令:
# - 手动运行: /system script run cloudflare-ddns
# - 查看日志: /log print where topics~"script"
# - 查看定时任务: /system scheduler print
# - 启用定时任务: /system scheduler enable cloudflare-ddns
# - 禁用定时任务: /system scheduler disable cloudflare-ddns
# - 查看接口状态: /interface print
# - 查看IP地址: /ip address print where interface=your-wan-interface
