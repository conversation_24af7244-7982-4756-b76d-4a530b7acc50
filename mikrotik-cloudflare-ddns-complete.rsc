# ============================================================================
# MikroTik RouterOS Cloudflare DDNS 完整脚本
# 版本: 2.0 - 生产就绪版本
# 兼容: RouterOS 7.x 及以上版本
# API: Cloudflare API v4 with Token Authentication
# ============================================================================

# ============================================================================
# 配置区域 - 请根据您的实际情况修改以下配置
# ============================================================================

# Cloudflare API Token (必须配置)
# 获取方式: https://dash.cloudflare.com/profile/api-tokens
# 权限要求: Zone:Zone:Read, Zone:DNS:Edit
:global cfApiToken "your_cloudflare_api_token_here"

# 要管理的域名列表 (必须配置)
# 格式: "域名1,域名2,域名3" (用逗号分隔，不要有空格)
# 示例: "example.com" 或 "example.com,mydomain.org"
:global cfDomains "example.com"

# 要管理的子域名列表 (必须配置)
# 格式: "子域名1,子域名2,子域名3" (用逗号分隔，不要有空格)
# "@" 表示根域名记录，留空或"@"都表示根域名
# 示例: "@,www" 或 "@,www,mail,ftp,api"
:global cfSubdomains "@,www"

# WAN接口名称 (必须配置)
# 常见值: "ether1", "pppoe-out1", "lte1", "bridge1"
# 查看可用接口: /interface print
:global wanInterface "ether1"

# 日志配置 (可选配置)
:global enableLogging true                    # 是否启用日志记录
:global logLevel "info"                       # 日志级别: debug, info, warning, error

# IP检测服务列表 (可选配置)
# 脚本会依次尝试这些服务获取公网IP
:global ipCheckServices "https://ipv4.icanhazip.com,https://api.ipify.org,https://checkip.amazonaws.com,https://ifconfig.me/ip"

# 高级选项 (可选配置)
:global forceUpdate false                     # 是否强制更新(忽略IP变化检测)
:global dnsTTL 300                           # DNS记录TTL值(秒)
:global maxRetries 3                         # API请求失败时的重试次数

# ============================================================================
# 全局变量定义 (请勿修改)
# ============================================================================

:global ddnsCurrentIP
:global ddnsLastIP
:global ddnsLogPrefix "[CLOUDFLARE-DDNS]"

# ============================================================================
# 日志记录函数 (请勿修改)
# ============================================================================

:global ddnsLog do={
    :local level $1
    :local message $2
    
    :if ($enableLogging) do={
        :local timestamp [/system clock get time]
        :local date [/system clock get date]
        :local logMsg "$ddnsLogPrefix [$date $timestamp] [$level] $message"
        
        :if ($level = "error") do={
            :log error $logMsg
        } else={
            :if ($level = "warning") do={
                :log warning $logMsg
            } else={
                :log info $logMsg
            }
        }
        
        # 同时输出到终端(如果是手动运行)
        :if ($level = "error") do={
            :put "❌ $message"
        } else={
            :if ($level = "warning") do={
                :put "⚠️  $message"
            } else={
                :if ($level = "info") do={
                    :put "ℹ️  $message"
                } else={
                    :put "🔍 $message"
                }
            }
        }
    }
}

# ============================================================================
# 获取公网IP地址函数 (请勿修改)
# ============================================================================

:global ddnsGetPublicIP do={
    :local services [:toarray $ipCheckServices]
    :local currentIP ""
    
    :foreach service in=$services do={
        :do {
            $ddnsLog "debug" "正在从 $service 获取公网IP地址..."
            :local result [/tool fetch url=$service as-value output=user]
            :if ($result->"status" = "finished") do={
                :set currentIP [:pick ($result->"data") 0 [:find ($result->"data") "\n"]]
                # 清理可能的回车符和空格
                :set currentIP [:tostr $currentIP]
                :if ([:len $currentIP] > 7 and [:len $currentIP] < 16) do={
                    # 简单验证IP格式
                    :if ([:find $currentIP "."] > 0) do={
                        $ddnsLog "info" "成功获取公网IP: $currentIP"
                        :return $currentIP
                    }
                }
            }
        } on-error={
            $ddnsLog "debug" "从 $service 获取IP失败，尝试下一个服务..."
        }
    }
    
    $ddnsLog "error" "所有IP检测服务均失败，请检查网络连接"
    :return ""
}

# ============================================================================
# 获取Cloudflare Zone ID函数 (请勿修改)
# ============================================================================

:global ddnsGetZoneID do={
    :local domain $1
    :local url "https://api.cloudflare.com/client/v4/zones?name=$domain"
    :local headers "Authorization: Bearer $cfApiToken,Content-Type: application/json"
    
    :local retryCount 0
    :while ($retryCount < $maxRetries) do={
        :do {
            $ddnsLog "debug" "正在获取域名 $domain 的Zone ID (尝试 $($retryCount + 1)/$maxRetries)..."
            :local result [/tool fetch url=$url http-header-field=$headers as-value output=user]
            
            :if ($result->"status" = "finished") do={
                :local response ($result->"data")
                
                # 检查API响应是否成功
                :if ([:find $response "\"success\":true"] >= 0) do={
                    # 提取Zone ID
                    :local zoneIdStart [:find $response "\"id\":\""]
                    :if ($zoneIdStart >= 0) do={
                        :set zoneIdStart ($zoneIdStart + 6)
                        :local zoneIdEnd [:find $response "\"" $zoneIdStart]
                        :local zoneId [:pick $response $zoneIdStart $zoneIdEnd]
                        $ddnsLog "debug" "域名 $domain 的Zone ID: $zoneId"
                        :return $zoneId
                    }
                } else={
                    $ddnsLog "error" "Cloudflare API返回错误: $response"
                }
            }
        } on-error={
            $ddnsLog "warning" "获取Zone ID失败 (尝试 $($retryCount + 1)/$maxRetries)"
        }
        :set retryCount ($retryCount + 1)
        :if ($retryCount < $maxRetries) do={
            :delay 2s
        }
    }
    
    $ddnsLog "error" "无法获取域名 $domain 的Zone ID，请检查域名是否在Cloudflare中托管"
    :return ""
}

# ============================================================================
# 获取DNS记录ID函数 (请勿修改)
# ============================================================================

:global ddnsGetRecordID do={
    :local zoneId $1
    :local recordName $2
    :local url "https://api.cloudflare.com/client/v4/zones/$zoneId/dns_records?name=$recordName&type=A"
    :local headers "Authorization: Bearer $cfApiToken,Content-Type: application/json"

    :local retryCount 0
    :while ($retryCount < $maxRetries) do={
        :do {
            $ddnsLog "debug" "正在获取记录 $recordName 的Record ID (尝试 $($retryCount + 1)/$maxRetries)..."
            :local result [/tool fetch url=$url http-header-field=$headers as-value output=user]

            :if ($result->"status" = "finished") do={
                :local response ($result->"data")

                # 检查API响应是否成功
                :if ([:find $response "\"success\":true"] >= 0) do={
                    # 提取Record ID
                    :local recordIdStart [:find $response "\"id\":\""]
                    :if ($recordIdStart >= 0) do={
                        :set recordIdStart ($recordIdStart + 6)
                        :local recordIdEnd [:find $response "\"" $recordIdStart]
                        :local recordId [:pick $response $recordIdStart $recordIdEnd]
                        $ddnsLog "debug" "记录 $recordName 的Record ID: $recordId"
                        :return $recordId
                    } else={
                        $ddnsLog "debug" "记录 $recordName 不存在，将创建新记录"
                        :return ""
                    }
                } else={
                    $ddnsLog "error" "获取DNS记录时API返回错误: $response"
                }
            }
        } on-error={
            $ddnsLog "warning" "获取Record ID失败 (尝试 $($retryCount + 1)/$maxRetries)"
        }
        :set retryCount ($retryCount + 1)
        :if ($retryCount < $maxRetries) do={
            :delay 2s
        }
    }

    $ddnsLog "warning" "无法获取记录 $recordName 的Record ID，将尝试创建新记录"
    :return ""
}

# ============================================================================
# 更新DNS记录函数 (请勿修改)
# ============================================================================

:global ddnsUpdateRecord do={
    :local zoneId $1
    :local recordId $2
    :local recordName $3
    :local newIP $4
    :local url "https://api.cloudflare.com/client/v4/zones/$zoneId/dns_records/$recordId"
    :local headers "Authorization: Bearer $cfApiToken,Content-Type: application/json"
    :local data "{\"type\":\"A\",\"name\":\"$recordName\",\"content\":\"$newIP\",\"ttl\":$dnsTTL}"

    :local retryCount 0
    :while ($retryCount < $maxRetries) do={
        :do {
            $ddnsLog "info" "正在更新DNS记录 $recordName 到 $newIP (尝试 $($retryCount + 1)/$maxRetries)..."
            :local result [/tool fetch url=$url http-method=put http-header-field=$headers http-data=$data as-value output=user]

            :if ($result->"status" = "finished") do={
                :local response ($result->"data")

                # 检查更新是否成功
                :if ([:find $response "\"success\":true"] >= 0) do={
                    $ddnsLog "info" "✅ DNS记录 $recordName 更新成功"
                    :return true
                } else={
                    $ddnsLog "error" "DNS记录 $recordName 更新失败: $response"
                }
            }
        } on-error={
            $ddnsLog "warning" "更新DNS记录失败 (尝试 $($retryCount + 1)/$maxRetries)"
        }
        :set retryCount ($retryCount + 1)
        :if ($retryCount < $maxRetries) do={
            :delay 2s
        }
    }

    $ddnsLog "error" "❌ 更新DNS记录 $recordName 最终失败"
    :return false
}

# ============================================================================
# 创建DNS记录函数 (请勿修改)
# ============================================================================

:global ddnsCreateRecord do={
    :local zoneId $1
    :local recordName $2
    :local newIP $3
    :local url "https://api.cloudflare.com/client/v4/zones/$zoneId/dns_records"
    :local headers "Authorization: Bearer $cfApiToken,Content-Type: application/json"
    :local data "{\"type\":\"A\",\"name\":\"$recordName\",\"content\":\"$newIP\",\"ttl\":$dnsTTL}"

    :local retryCount 0
    :while ($retryCount < $maxRetries) do={
        :do {
            $ddnsLog "info" "正在创建DNS记录 $recordName (尝试 $($retryCount + 1)/$maxRetries)..."
            :local result [/tool fetch url=$url http-method=post http-header-field=$headers http-data=$data as-value output=user]

            :if ($result->"status" = "finished") do={
                :local response ($result->"data")

                # 检查创建是否成功
                :if ([:find $response "\"success\":true"] >= 0) do={
                    $ddnsLog "info" "✅ DNS记录 $recordName 创建成功"
                    :return true
                } else={
                    $ddnsLog "error" "DNS记录 $recordName 创建失败: $response"
                }
            }
        } on-error={
            $ddnsLog "warning" "创建DNS记录失败 (尝试 $($retryCount + 1)/$maxRetries)"
        }
        :set retryCount ($retryCount + 1)
        :if ($retryCount < $maxRetries) do={
            :delay 2s
        }
    }

    $ddnsLog "error" "❌ 创建DNS记录 $recordName 最终失败"
    :return false
}

# ============================================================================
# 处理单个域名的所有子域名函数 (请勿修改)
# ============================================================================

:global ddnsProcessDomain do={
    :local domain $1
    :local newIP $2
    :local subdomains [:toarray $cfSubdomains]

    $ddnsLog "info" "🌐 开始处理域名: $domain"

    # 获取Zone ID
    :local zoneId [$ddnsGetZoneID $domain]
    :if ([:len $zoneId] = 0) do={
        $ddnsLog "error" "❌ 无法获取域名 $domain 的Zone ID，跳过此域名"
        :return false
    }

    :local successCount 0
    :local totalCount [:len $subdomains]

    # 处理每个子域名
    :foreach subdomain in=$subdomains do={
        :local recordName ""
        :if ($subdomain = "@" or [:len $subdomain] = 0) do={
            :set recordName $domain
            $ddnsLog "debug" "处理根域名记录: $recordName"
        } else={
            :set recordName "$subdomain.$domain"
            $ddnsLog "debug" "处理子域名记录: $recordName"
        }

        # 获取记录ID
        :local recordId [$ddnsGetRecordID $zoneId $recordName]

        :if ([:len $recordId] > 0) do={
            # 记录存在，更新它
            :if ([$ddnsUpdateRecord $zoneId $recordId $recordName $newIP]) do={
                :set successCount ($successCount + 1)
            }
        } else={
            # 记录不存在，创建它
            :if ([$ddnsCreateRecord $zoneId $recordName $newIP]) do={
                :set successCount ($successCount + 1)
            }
        }

        # 在处理记录之间添加短暂延迟，避免API限制
        :delay 1s
    }

    $ddnsLog "info" "📊 域名 $domain 处理完成: $successCount/$totalCount 个记录成功更新"
    :return ($successCount > 0)
}

# ============================================================================
# 配置验证函数 (请勿修改)
# ============================================================================

:global ddnsValidateConfig do={
    $ddnsLog "info" "🔍 开始验证配置..."

    # 验证API Token
    :if ([:len $cfApiToken] = 0 or $cfApiToken = "your_cloudflare_api_token_here") do={
        $ddnsLog "error" "❌ 请配置有效的Cloudflare API Token"
        $ddnsLog "info" "获取Token: https://dash.cloudflare.com/profile/api-tokens"
        :return false
    }

    # 验证域名配置
    :if ([:len $cfDomains] = 0 or $cfDomains = "example.com") do={
        $ddnsLog "error" "❌ 请配置要管理的域名列表"
        :return false
    }

    # 验证子域名配置
    :if ([:len $cfSubdomains] = 0) do={
        $ddnsLog "error" "❌ 请配置要管理的子域名列表"
        :return false
    }

    # 验证WAN接口
    :local interfaceExists false
    :foreach interface in=[/interface find] do={
        :if ([/interface get $interface name] = $wanInterface) do={
            :set interfaceExists true
        }
    }
    :if (!$interfaceExists) do={
        $ddnsLog "error" "❌ WAN接口 '$wanInterface' 不存在"
        $ddnsLog "info" "可用接口列表:"
        /interface print brief where !disabled
        :return false
    }

    $ddnsLog "info" "✅ 配置验证通过"
    :return true
}

# ============================================================================
# 主执行逻辑开始 (请勿修改)
# ============================================================================

$ddnsLog "info" "🚀 === Cloudflare DDNS 脚本开始执行 ==="
$ddnsLog "info" "📅 执行时间: [/system clock get date] [/system clock get time]"

# 验证配置
:if (![$ddnsValidateConfig]) do={
    $ddnsLog "error" "❌ 配置验证失败，脚本终止"
    $ddnsLog "info" "请检查脚本顶部的配置区域并修改相应参数"
    :error "配置错误"
}

# 显示当前配置信息
$ddnsLog "info" "📋 当前配置:"
$ddnsLog "info" "   - 域名: $cfDomains"
$ddnsLog "info" "   - 子域名: $cfSubdomains"
$ddnsLog "info" "   - WAN接口: $wanInterface"
$ddnsLog "info" "   - 强制更新: $forceUpdate"

# 获取当前公网IP
$ddnsLog "info" "🌍 正在获取当前公网IP地址..."
:set ddnsCurrentIP [$ddnsGetPublicIP]
:if ([:len $ddnsCurrentIP] = 0) do={
    $ddnsLog "error" "❌ 无法获取当前公网IP地址，脚本终止"
    $ddnsLog "info" "请检查网络连接和防火墙设置"
    :error "网络错误：无法获取公网IP"
}

$ddnsLog "info" "🌐 当前公网IP: $ddnsCurrentIP"

# 检查IP是否发生变化
:local needUpdate $forceUpdate
:if (!$needUpdate) do={
    :if ([:len $ddnsLastIP] = 0) do={
        :set needUpdate true
        $ddnsLog "info" "🆕 首次运行，将更新所有DNS记录"
    } else={
        :if ($ddnsCurrentIP != $ddnsLastIP) do={
            :set needUpdate true
            $ddnsLog "info" "🔄 检测到IP地址变化: $ddnsLastIP → $ddnsCurrentIP"
        } else={
            $ddnsLog "info" "✅ IP地址未发生变化: $ddnsCurrentIP，跳过更新"
        }
    }
} else={
    $ddnsLog "info" "🔄 强制更新模式已启用"
}

# 如果需要更新DNS记录
:if ($needUpdate) do={
    :local domains [:toarray $cfDomains]
    :local totalDomains [:len $domains]
    :local successDomains 0

    $ddnsLog "info" "📝 开始更新 $totalDomains 个域名的DNS记录..."

    # 处理每个域名
    :foreach domain in=$domains do={
        :if ([$ddnsProcessDomain $domain $ddnsCurrentIP]) do={
            :set successDomains ($successDomains + 1)
        }

        # 在处理域名之间添加延迟，避免API限制
        :if ($totalDomains > 1) do={
            :delay 2s
        }
    }

    # 更新记录的IP地址
    :set ddnsLastIP $ddnsCurrentIP

    # 输出最终结果
    $ddnsLog "info" "📊 DNS更新统计: $successDomains/$totalDomains 个域名成功更新"

    :if ($successDomains > 0) do={
        :if ($successDomains = $totalDomains) do={
            $ddnsLog "info" "🎉 === 所有域名DNS记录更新成功 ==="
        } else={
            $ddnsLog "warning" "⚠️  === 部分域名DNS记录更新成功 ==="
        }
    } else={
        $ddnsLog "error" "❌ === 所有域名DNS记录更新失败 ==="
    }
} else={
    $ddnsLog "info" "✅ === DDNS脚本执行完成（无需更新）==="
}

$ddnsLog "info" "🏁 脚本执行结束"

# ============================================================================
# 使用说明和管理命令
# ============================================================================

# 脚本使用说明:
# 1. 修改脚本顶部配置区域的参数
# 2. 将脚本导入到MikroTik设备: /import script-name.rsc
# 3. 或直接在终端中粘贴运行
# 4. 设置定时任务: /system scheduler add name="cloudflare-ddns" interval=10m on-event="/system script run cloudflare-ddns"
#
# 管理命令:
# - 手动运行: /system script run cloudflare-ddns
# - 查看日志: /log print where topics~"script"
# - 查看定时任务: /system scheduler print
# - 启用定时任务: /system scheduler enable cloudflare-ddns
# - 禁用定时任务: /system scheduler disable cloudflare-ddns
