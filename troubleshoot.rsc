# ============================================================================
# MikroTik RouterOS Cloudflare DDNS 故障排除脚本
# 版本: 2.0
# 用途: 诊断和解决DDNS脚本问题
# ============================================================================

:put "=== MikroTik Cloudflare DDNS 故障排除工具 ==="
:put ""

# 系统信息检查
:put "1. 系统信息检查"
:put "----------------------------------------"
:local rosVersion [/system resource get version]
:local architecture [/system resource get architecture-name]
:local uptime [/system resource get uptime]
:put "RouterOS版本: $rosVersion"
:put "架构: $architecture"
:put "运行时间: $uptime"

:if ([:pick $rosVersion 0 1] < "7") do={
    :put "❌ 错误: RouterOS版本过低，需要7.0或更高版本"
} else={
    :put "✓ RouterOS版本兼容"
}
:put ""

# 网络连接检查
:put "2. 网络连接检查"
:put "----------------------------------------"

# 检查DNS解析
:put "检查DNS解析..."
:do {
    :resolve "api.cloudflare.com"
    :put "✓ DNS解析正常"
} on-error={
    :put "❌ DNS解析失败"
}

# 检查Cloudflare API连接
:put "检查Cloudflare API连接..."
:do {
    :local result [/tool fetch url="https://api.cloudflare.com/client/v4/" as-value output=user]
    :if ($result->"status" = "finished") do={
        :put "✓ Cloudflare API连接正常"
    } else={
        :put "❌ Cloudflare API连接失败"
    }
} on-error={
    :put "❌ 无法连接到Cloudflare API"
}

# 检查IP检测服务
:put "检查IP检测服务..."
:local ipServices {"https://api.ipify.org"; "https://ipv4.icanhazip.com"; "https://checkip.amazonaws.com"}
:local workingServices 0

:foreach service in=$ipServices do={
    :do {
        :local result [/tool fetch url=$service as-value output=user]
        :if ($result->"status" = "finished") do={
            :local ip [:pick ($result->"data") 0 [:find ($result->"data") "\n"]]
            :put "✓ $service - IP: $ip"
            :set workingServices ($workingServices + 1)
        }
    } on-error={
        :put "❌ $service - 连接失败"
    }
}

:if ($workingServices > 0) do={
    :put "✓ $workingServices 个IP检测服务可用"
} else={
    :put "❌ 所有IP检测服务均不可用"
}
:put ""

# 脚本状态检查
:put "3. 脚本状态检查"
:put "----------------------------------------"

# 检查脚本是否存在
:local scriptExists false
:foreach script in=[/system script find] do={
    :if ([/system script get $script name] = "cloudflare-ddns") do={
        :set scriptExists true
        :put "✓ DDNS脚本已安装"
        
        # 检查脚本内容
        :local scriptSource [/system script get $script source]
        :if ([:find $scriptSource "cfApiToken"] >= 0) do={
            :put "✓ 脚本包含API Token配置"
        } else={
            :put "❌ 脚本缺少API Token配置"
        }
        
        :if ([:find $scriptSource "cfDomains"] >= 0) do={
            :put "✓ 脚本包含域名配置"
        } else={
            :put "❌ 脚本缺少域名配置"
        }
    }
}

:if (!$scriptExists) do={
    :put "❌ DDNS脚本未安装"
}

# 检查定时任务
:local schedulerExists false
:foreach scheduler in=[/system scheduler find] do={
    :if ([/system scheduler get $scheduler name] = "cloudflare-ddns") do={
        :set schedulerExists true
        :local interval [/system scheduler get $scheduler interval]
        :local disabled [/system scheduler get $scheduler disabled]
        :put "✓ 定时任务已配置，间隔: $interval"
        :if ($disabled) do={
            :put "⚠ 定时任务已禁用"
        } else={
            :put "✓ 定时任务已启用"
        }
    }
}

:if (!$schedulerExists) do={
    :put "❌ 定时任务未配置"
}
:put ""

# 日志检查
:put "4. 日志检查"
:put "----------------------------------------"
:put "最近的DDNS相关日志:"

:local logCount 0
:foreach logEntry in=[/log find where topics~"script"] do={
    :local message [/log get $logEntry message]
    :if ([:find $message "[DDNS]"] >= 0) do={
        :local time [/log get $logEntry time]
        :local topics [/log get $logEntry topics]
        :put "$time [$topics] $message"
        :set logCount ($logCount + 1)
        :if ($logCount >= 10) do={
            :put "... (显示最近10条记录)"
            break
        }
    }
}

:if ($logCount = 0) do={
    :put "❌ 未找到DDNS相关日志"
}
:put ""

# API Token测试
:put "5. API Token测试"
:put "----------------------------------------"

# 尝试从脚本中提取API Token
:local apiToken ""
:if ($scriptExists) do={
    :local scriptSource [/system script get [/system script find where name="cloudflare-ddns"] source]
    :local tokenStart [:find $scriptSource "cfApiToken \""]
    :if ($tokenStart >= 0) do={
        :set tokenStart ($tokenStart + 13)
        :local tokenEnd [:find $scriptSource "\"" $tokenStart]
        :set apiToken [:pick $scriptSource $tokenStart $tokenEnd]
    }
}

:if ([:len $apiToken] > 0 and $apiToken != "your_cloudflare_api_token_here") do={
    :put "正在测试API Token..."
    :do {
        :local headers "Authorization: Bearer $apiToken,Content-Type: application/json"
        :local result [/tool fetch url="https://api.cloudflare.com/client/v4/user/tokens/verify" http-header-field=$headers as-value output=user]
        
        :if ($result->"status" = "finished") do={
            :local response ($result->"data")
            :if ([:find $response "\"success\":true"] >= 0) do={
                :put "✓ API Token有效"
            } else={
                :put "❌ API Token无效"
                :put "响应: $response"
            }
        }
    } on-error={
        :put "❌ API Token测试失败"
    }
} else={
    :put "❌ 未找到有效的API Token配置"
}
:put ""

# 接口检查
:put "6. 网络接口检查"
:put "----------------------------------------"
:put "当前网络接口状态:"
/interface print brief where !disabled

:put ""
:put "WAN接口IP地址:"
:foreach interface in=[/interface find where !disabled] do={
    :local interfaceName [/interface get $interface name]
    :local addresses [/ip address find where interface=$interfaceName]
    :if ([:len $addresses] > 0) do={
        :foreach addr in=$addresses do={
            :local address [/ip address get $addr address]
            :put "$interfaceName: $address"
        }
    }
}
:put ""

# 修复建议
:put "7. 修复建议"
:put "----------------------------------------"

:if (!$scriptExists) do={
    :put "• 运行安装脚本: /import install.rsc"
}

:if (!$schedulerExists) do={
    :put "• 创建定时任务: /system scheduler add name=\"cloudflare-ddns\" interval=10m on-event=\"/system script run cloudflare-ddns\""
}

:if ($workingServices = 0) do={
    :put "• 检查防火墙设置，确保允许HTTPS出站连接"
    :put "• 检查DNS设置，确保可以解析外部域名"
}

:put "• 查看详细日志: /log print where topics~\"script\""
:put "• 手动运行脚本: /system script run cloudflare-ddns"
:put "• 启用调试模式: 在脚本中设置 logLevel \"debug\""
:put ""

# 快速测试
:put "8. 快速测试"
:put "----------------------------------------"
:local runTest [/terminal ask "是否运行DDNS脚本测试? (y/n): "]

:if ($runTest = "y" or $runTest = "Y") do={
    :if ($scriptExists) do={
        :put "正在运行DDNS脚本..."
        :do {
            /system script run cloudflare-ddns
            :put "✓ 脚本运行完成，请检查日志"
        } on-error={
            :put "❌ 脚本运行失败"
        }
    } else={
        :put "❌ 脚本未安装，无法运行测试"
    }
}

:put ""
:put "=== 故障排除完成 ==="
:put ""
:put "如果问题仍然存在，请："
:put "1. 检查Cloudflare控制台中的域名设置"
:put "2. 验证API Token权限"
:put "3. 查看RouterOS系统日志"
:put "4. 联系技术支持"
