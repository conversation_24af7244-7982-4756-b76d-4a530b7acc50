# ============================================================================
# MikroTik RouterOS Cloudflare DDNS 脚本
# 版本: 2.0
# 兼容: RouterOS 7.x 及以上版本
# 作者: 基于Cloudflare API v4规范开发
# ============================================================================

# ============================================================================
# 配置区域 - 请根据您的实际情况修改以下配置
# ============================================================================

# Cloudflare API配置
:global cfApiToken "your_cloudflare_api_token_here"
:global cfEmail "<EMAIL>"

# 域名配置 - 支持多个根域名
# 格式: "域名1,域名2,域名3"
:global cfDomains "example.com,mydomain.org"

# 子域名配置 - 支持每个域名下的多个子域名
# 格式: "子域名1,子域名2,子域名3" (不包含根域名，留空表示根域名记录)
:global cfSubdomains "www,mail,ftp,api,@"

# 网络接口配置
:global wanInterface "ether1"  # WAN口接口名称，请根据实际情况修改

# 日志配置
:global enableLogging true     # 是否启用日志记录
:global logLevel "info"        # 日志级别: debug, info, warning, error

# IP检测配置
:global ipCheckServices "https://ipv4.icanhazip.com,https://api.ipify.org,https://checkip.amazonaws.com"
:global forceUpdate false      # 是否强制更新（忽略IP变化检测）

# ============================================================================
# 全局变量定义
# ============================================================================

:global ddnsCurrentIP
:global ddnsLastIP
:global ddnsLogPrefix "[DDNS]"

# ============================================================================
# 日志记录函数
# ============================================================================

:global ddnsLog do={
    :local level $1
    :local message $2
    
    :if ($enableLogging) do={
        :local timestamp [/system clock get time]
        :local date [/system clock get date]
        :local logMsg "$ddnsLogPrefix [$date $timestamp] [$level] $message"
        
        :if ($level = "error") do={
            :log error $logMsg
        } else={
            :if ($level = "warning") do={
                :log warning $logMsg
            } else={
                :log info $logMsg
            }
        }
    }
}

# ============================================================================
# 获取公网IP地址函数
# ============================================================================

:global ddnsGetPublicIP do={
    :local services [:toarray $ipCheckServices]
    :local currentIP ""
    
    :foreach service in=$services do={
        :do {
            $ddnsLog "info" "正在从 $service 获取公网IP地址..."
            :local result [/tool fetch url=$service as-value output=user]
            :if ($result->"status" = "finished") do={
                :set currentIP [:pick ($result->"data") 0 [:find ($result->"data") "\n"]]
                :if ([:len $currentIP] > 0) do={
                    $ddnsLog "info" "成功获取公网IP: $currentIP"
                    :return $currentIP
                }
            }
        } on-error={
            $ddnsLog "warning" "从 $service 获取IP失败，尝试下一个服务..."
        }
    }
    
    $ddnsLog "error" "所有IP检测服务均失败"
    :return ""
}

# ============================================================================
# 获取Cloudflare Zone ID函数
# ============================================================================

:global ddnsGetZoneID do={
    :local domain $1
    :local url "https://api.cloudflare.com/client/v4/zones?name=$domain"
    :local headers "Authorization: Bearer $cfApiToken,Content-Type: application/json"
    
    :do {
        $ddnsLog "debug" "正在获取域名 $domain 的Zone ID..."
        :local result [/tool fetch url=$url http-header-field=$headers as-value output=user]
        
        :if ($result->"status" = "finished") do={
            :local response ($result->"data")
            
            # 简单的JSON解析来提取Zone ID
            :local zoneIdStart [:find $response "\"id\":\""]
            :if ($zoneIdStart >= 0) do={
                :set zoneIdStart ($zoneIdStart + 6)
                :local zoneIdEnd [:find $response "\"" $zoneIdStart]
                :local zoneId [:pick $response $zoneIdStart $zoneIdEnd]
                $ddnsLog "debug" "域名 $domain 的Zone ID: $zoneId"
                :return $zoneId
            }
        }
    } on-error={
        $ddnsLog "error" "获取域名 $domain 的Zone ID失败"
    }
    
    :return ""
}

# ============================================================================
# 获取DNS记录ID函数
# ============================================================================

:global ddnsGetRecordID do={
    :local zoneId $1
    :local recordName $2
    :local url "https://api.cloudflare.com/client/v4/zones/$zoneId/dns_records?name=$recordName&type=A"
    :local headers "Authorization: Bearer $cfApiToken,Content-Type: application/json"
    
    :do {
        $ddnsLog "debug" "正在获取记录 $recordName 的Record ID..."
        :local result [/tool fetch url=$url http-header-field=$headers as-value output=user]
        
        :if ($result->"status" = "finished") do={
            :local response ($result->"data")
            
            # 简单的JSON解析来提取Record ID
            :local recordIdStart [:find $response "\"id\":\""]
            :if ($recordIdStart >= 0) do={
                :set recordIdStart ($recordIdStart + 6)
                :local recordIdEnd [:find $response "\"" $recordIdStart]
                :local recordId [:pick $response $recordIdStart $recordIdEnd]
                $ddnsLog "debug" "记录 $recordName 的Record ID: $recordId"
                :return $recordId
            }
        }
    } on-error={
        $ddnsLog "error" "获取记录 $recordName 的Record ID失败"
    }
    
    :return ""
}

# ============================================================================
# 更新DNS记录函数
# ============================================================================

:global ddnsUpdateRecord do={
    :local zoneId $1
    :local recordId $2
    :local recordName $3
    :local newIP $4
    :local url "https://api.cloudflare.com/client/v4/zones/$zoneId/dns_records/$recordId"
    :local headers "Authorization: Bearer $cfApiToken,Content-Type: application/json"
    :local data "{\"type\":\"A\",\"name\":\"$recordName\",\"content\":\"$newIP\",\"ttl\":300}"

    :do {
        $ddnsLog "info" "正在更新DNS记录 $recordName 到 $newIP..."
        :local result [/tool fetch url=$url http-method=put http-header-field=$headers http-data=$data as-value output=user]

        :if ($result->"status" = "finished") do={
            :local response ($result->"data")

            # 检查是否更新成功
            :if ([:find $response "\"success\":true"] >= 0) do={
                $ddnsLog "info" "DNS记录 $recordName 更新成功"
                :return true
            } else={
                $ddnsLog "error" "DNS记录 $recordName 更新失败: $response"
                :return false
            }
        }
    } on-error={
        $ddnsLog "error" "更新DNS记录 $recordName 时发生错误"
    }

    :return false
}

# ============================================================================
# 创建DNS记录函数（如果记录不存在）
# ============================================================================

:global ddnsCreateRecord do={
    :local zoneId $1
    :local recordName $2
    :local newIP $3
    :local url "https://api.cloudflare.com/client/v4/zones/$zoneId/dns_records"
    :local headers "Authorization: Bearer $cfApiToken,Content-Type: application/json"
    :local data "{\"type\":\"A\",\"name\":\"$recordName\",\"content\":\"$newIP\",\"ttl\":300}"

    :do {
        $ddnsLog "info" "正在创建DNS记录 $recordName..."
        :local result [/tool fetch url=$url http-method=post http-header-field=$headers http-data=$data as-value output=user]

        :if ($result->"status" = "finished") do={
            :local response ($result->"data")

            # 检查是否创建成功
            :if ([:find $response "\"success\":true"] >= 0) do={
                $ddnsLog "info" "DNS记录 $recordName 创建成功"
                :return true
            } else={
                $ddnsLog "error" "DNS记录 $recordName 创建失败: $response"
                :return false
            }
        }
    } on-error={
        $ddnsLog "error" "创建DNS记录 $recordName 时发生错误"
    }

    :return false
}

# ============================================================================
# 处理单个域名的所有子域名
# ============================================================================

:global ddnsProcessDomain do={
    :local domain $1
    :local newIP $2
    :local subdomains [:toarray $cfSubdomains]

    $ddnsLog "info" "开始处理域名: $domain"

    # 获取Zone ID
    :local zoneId [$ddnsGetZoneID $domain]
    :if ([:len $zoneId] = 0) do={
        $ddnsLog "error" "无法获取域名 $domain 的Zone ID，跳过此域名"
        :return false
    }

    :local successCount 0
    :local totalCount [:len $subdomains]

    # 处理每个子域名
    :foreach subdomain in=$subdomains do={
        :local recordName ""
        :if ($subdomain = "@" or [:len $subdomain] = 0) do={
            :set recordName $domain
        } else={
            :set recordName "$subdomain.$domain"
        }

        # 获取记录ID
        :local recordId [$ddnsGetRecordID $zoneId $recordName]

        :if ([:len $recordId] > 0) do={
            # 记录存在，更新它
            :if ([$ddnsUpdateRecord $zoneId $recordId $recordName $newIP]) do={
                :set successCount ($successCount + 1)
            }
        } else={
            # 记录不存在，创建它
            :if ([$ddnsCreateRecord $zoneId $recordName $newIP]) do={
                :set successCount ($successCount + 1)
            }
        }
    }

    $ddnsLog "info" "域名 $domain 处理完成: $successCount/$totalCount 个记录成功更新"
    :return ($successCount > 0)
}

# ============================================================================
# 主执行逻辑
# ============================================================================

$ddnsLog "info" "=== Cloudflare DDNS 脚本开始执行 ==="

# 验证配置
:if ([:len $cfApiToken] = 0 or $cfApiToken = "your_cloudflare_api_token_here") do={
    $ddnsLog "error" "请配置有效的Cloudflare API Token"
    :error "配置错误：缺少API Token"
}

:if ([:len $cfDomains] = 0) do={
    $ddnsLog "error" "请配置要管理的域名列表"
    :error "配置错误：缺少域名配置"
}

# 获取当前公网IP
:set ddnsCurrentIP [$ddnsGetPublicIP]
:if ([:len $ddnsCurrentIP] = 0) do={
    $ddnsLog "error" "无法获取当前公网IP地址"
    :error "网络错误：无法获取公网IP"
}

# 检查IP是否发生变化
:local needUpdate $forceUpdate
:if (!$needUpdate) do={
    :if ([:len $ddnsLastIP] = 0 or $ddnsCurrentIP != $ddnsLastIP) do={
        :set needUpdate true
        $ddnsLog "info" "检测到IP地址变化: $ddnsLastIP -> $ddnsCurrentIP"
    } else={
        $ddnsLog "info" "IP地址未发生变化: $ddnsCurrentIP，跳过更新"
    }
}

# 如果需要更新
:if ($needUpdate) do={
    :local domains [:toarray $cfDomains]
    :local totalDomains [:len $domains]
    :local successDomains 0

    $ddnsLog "info" "开始更新 $totalDomains 个域名的DNS记录..."

    # 处理每个域名
    :foreach domain in=$domains do={
        :if ([$ddnsProcessDomain $domain $ddnsCurrentIP]) do={
            :set successDomains ($successDomains + 1)
        }
    }

    # 更新记录的IP地址
    :set ddnsLastIP $ddnsCurrentIP

    $ddnsLog "info" "DNS更新完成: $successDomains/$totalDomains 个域名成功更新"

    :if ($successDomains > 0) do={
        $ddnsLog "info" "=== DDNS更新成功完成 ==="
    } else={
        $ddnsLog "error" "=== DDNS更新失败 ==="
    }
} else={
    $ddnsLog "info" "=== DDNS脚本执行完成（无需更新）==="
}
