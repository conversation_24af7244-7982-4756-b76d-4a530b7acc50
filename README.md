# MikroTik RouterOS Cloudflare DDNS 脚本

一个功能完整的MikroTik RouterOS动态DNS脚本，支持Cloudflare API v4，可同时管理多个域名和子域名。

## 功能特性

- ✅ **多域名支持** - 同时管理多个根域名
- ✅ **多子域名支持** - 每个域名下支持多个子域名
- ✅ **Cloudflare API v4** - 使用最新的API规范
- ✅ **API Token认证** - 安全的Token认证方式
- ✅ **智能IP检测** - 多服务源IP检测，提高可靠性
- ✅ **变化检测** - 仅在IP变化时更新，减少API调用
- ✅ **错误处理** - 完善的错误处理和重试机制
- ✅ **详细日志** - 可配置的日志级别和详细记录
- ✅ **自动创建记录** - 自动创建不存在的DNS记录
- ✅ **兼容性强** - 支持RouterOS 7.x及以上版本

## 系统要求

- MikroTik RouterOS 7.0 或更高版本
- 互联网连接
- Cloudflare账户和API Token
- 域名已托管在Cloudflare

## 快速开始

### 1. 获取Cloudflare API Token

1. 登录 [Cloudflare控制台](https://dash.cloudflare.com/)
2. 点击右上角头像 → "My Profile"
3. 选择 "API Tokens" 标签
4. 点击 "Create Token"
5. 选择 "Custom token" 模板
6. 配置权限：
   - **Zone** - `Zone:Read`
   - **Zone** - `DNS:Edit`
7. 配置区域资源：
   - **Include** - `All zones` 或选择特定域名
8. 点击 "Continue to summary" → "Create Token"
9. 复制生成的Token（请妥善保存）

### 2. 下载和配置脚本

1. 下载 `cloudflare-ddns.rsc` 脚本文件
2. 使用文本编辑器打开脚本
3. 修改配置区域的以下参数：

```routeros
# 必须修改的配置
:global cfApiToken "your_cloudflare_api_token_here"  # 替换为您的API Token
:global cfDomains "example.com"                      # 替换为您的域名
:global cfSubdomains "@,www"                         # 配置要管理的子域名
:global wanInterface "ether1"                        # 设置正确的WAN接口
```

### 3. 上传和运行脚本

#### 方法1：通过WinBox上传

1. 打开WinBox，连接到您的MikroTik设备
2. 点击 "Files" 菜单
3. 将 `cloudflare-ddns.rsc` 文件拖拽到文件列表中
4. 打开 "Terminal"
5. 执行命令：`/import cloudflare-ddns.rsc`

#### 方法2：通过SSH/Telnet

1. 使用SSH或Telnet连接到MikroTik设备
2. 将脚本内容复制粘贴到终端中
3. 或者使用 `/import` 命令导入文件

#### 方法3：通过Web界面

1. 登录MikroTik Web界面
2. 进入 "System" → "Scripts"
3. 点击 "Add New"
4. 将脚本内容粘贴到脚本编辑器中
5. 设置脚本名称（如：cloudflare-ddns）
6. 点击 "OK" 保存

### 4. 测试脚本

在终端中运行以下命令测试脚本：

```routeros
/system script run cloudflare-ddns
```

检查系统日志：

```routeros
/log print where topics~"script"
```

## 详细配置说明

### 域名配置

```routeros
# 单个域名
:global cfDomains "example.com"

# 多个域名（用逗号分隔，不要有空格）
:global cfDomains "example.com,mydomain.org,testsite.net"
```

### 子域名配置

```routeros
# 基本配置（根域名和www）
:global cfSubdomains "@,www"

# 完整配置（包含常用服务）
:global cfSubdomains "@,www,mail,ftp,ssh,vpn,api,admin"

# 说明：
# "@" 表示根域名记录（example.com）
# "www" 表示www子域名（www.example.com）
```

### 网络接口配置

根据您的网络配置选择正确的WAN接口：

```routeros
# 有线以太网
:global wanInterface "ether1"

# PPPoE拨号
:global wanInterface "pppoe-out1"

# LTE移动网络
:global wanInterface "lte1"

# 桥接接口
:global wanInterface "bridge1"
```

查看可用接口：

```routeros
/interface print
```

### 日志配置

```routeros
# 启用日志
:global enableLogging true

# 日志级别
:global logLevel "info"    # debug, info, warning, error
```

## 设置定时任务

### 创建定时任务

```routeros
# 每5分钟检查一次
/system scheduler add name="cloudflare-ddns" interval=5m on-event="/system script run cloudflare-ddns"

# 每10分钟检查一次
/system scheduler add name="cloudflare-ddns" interval=10m on-event="/system script run cloudflare-ddns"

# 每小时检查一次
/system scheduler add name="cloudflare-ddns" interval=1h on-event="/system script run cloudflare-ddns"
```

### 管理定时任务

```routeros
# 查看定时任务
/system scheduler print

# 启用定时任务
/system scheduler enable cloudflare-ddns

# 禁用定时任务
/system scheduler disable cloudflare-ddns

# 删除定时任务
/system scheduler remove cloudflare-ddns
```

## 故障排除

### 常见问题

#### 1. API Token错误

**错误信息：** "Authentication error" 或 "Invalid token"

**解决方法：**
- 检查API Token是否正确复制
- 确认Token权限包含 Zone:Read 和 DNS:Edit
- 验证Token是否已过期

#### 2. 域名Zone ID获取失败

**错误信息：** "无法获取域名的Zone ID"

**解决方法：**
- 确认域名已在Cloudflare中托管
- 检查域名拼写是否正确
- 验证API Token权限

#### 3. 无法获取公网IP

**错误信息：** "所有IP检测服务均失败"

**解决方法：**
- 检查网络连接
- 确认防火墙设置允许HTTPS访问
- 尝试手动访问IP检测服务

#### 4. DNS记录更新失败

**错误信息：** "DNS记录更新失败"

**解决方法：**
- 检查子域名配置是否正确
- 确认API Token有DNS编辑权限
- 查看详细错误信息

### 调试方法

#### 1. 启用调试日志

```routeros
:global logLevel "debug"
```

#### 2. 查看详细日志

```routeros
# 查看所有日志
/log print

# 查看脚本相关日志
/log print where topics~"script"

# 查看最近的日志
/log print where time>([/system clock get time] - 00:10:00)
```

#### 3. 手动测试IP检测

```routeros
/tool fetch url="https://api.ipify.org" dst-path=ip.txt
/file print where name="ip.txt"
/file get ip.txt contents
```

#### 4. 测试API连接

```routeros
/tool fetch url="https://api.cloudflare.com/client/v4/user/tokens/verify" http-header-field="Authorization: Bearer YOUR_TOKEN" dst-path=test.txt
/file get test.txt contents
```

## 高级配置

### 自定义IP检测服务

```routeros
:global ipCheckServices "https://ipv4.icanhazip.com,https://api.ipify.org,https://checkip.amazonaws.com,https://ifconfig.me/ip"
```

### 强制更新模式

```routeros
# 每次运行都更新DNS记录（忽略IP变化检测）
:global forceUpdate true
```

### 自定义TTL值

```routeros
# 在更新DNS记录函数中修改TTL值
:local data "{\"type\":\"A\",\"name\":\"$recordName\",\"content\":\"$newIP\",\"ttl\":300}"
```

## 安全建议

1. **API Token权限最小化** - 仅授予必要的权限
2. **定期更新Token** - 建议定期更换API Token
3. **监控日志** - 定期检查脚本运行日志
4. **备份配置** - 保存脚本配置的备份
5. **网络安全** - 确保MikroTik设备的网络安全

## 版本历史

- **v2.0** - 支持多域名、多子域名，完善错误处理
- **v1.5** - 添加IP变化检测，优化API调用
- **v1.0** - 基础DDNS功能实现

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 支持

如果您在使用过程中遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查系统日志获取详细错误信息
3. 在GitHub上提交Issue

---

**注意：** 请确保您的MikroTik设备有稳定的互联网连接，并且Cloudflare API服务可正常访问。
