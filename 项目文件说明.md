# MikroTik RouterOS Cloudflare DDNS 项目文件说明

本项目包含了完整的MikroTik RouterOS动态DNS解决方案，支持Cloudflare API v4和多域名管理。

## 文件列表

### 1. 主要脚本文件

#### `cloudflare-ddns.rsc`
- **用途**: 主要的DDNS脚本文件
- **功能**: 
  - 自动检测公网IP变化
  - 通过Cloudflare API更新DNS记录
  - 支持多域名和多子域名管理
  - 完整的错误处理和日志记录
- **适用场景**: 需要完整功能和自定义配置的用户
- **使用方法**: 
  1. 修改配置区域的参数
  2. 导入到MikroTik设备
  3. 设置定时任务

#### `quick-setup.rsc`
- **用途**: 快速配置和部署脚本
- **功能**:
  - 简化的配置流程
  - 自动生成完整的DDNS脚本
  - 自动创建定时任务
- **适用场景**: 有经验的用户快速部署
- **使用方法**:
  1. 修改脚本顶部的配置参数
  2. 直接运行脚本完成安装

#### `install.rsc`
- **用途**: 交互式安装向导
- **功能**:
  - 引导式配置流程
  - 系统兼容性检查
  - 网络连接测试
  - 自动生成配置
- **适用场景**: 新手用户或需要引导安装的场景
- **使用方法**: 直接运行脚本，按提示操作

#### `troubleshoot.rsc`
- **用途**: 故障排除和诊断工具
- **功能**:
  - 系统状态检查
  - 网络连接诊断
  - API Token验证
  - 配置验证
  - 修复建议
- **适用场景**: 遇到问题时的诊断工具
- **使用方法**: 运行脚本查看诊断结果

### 2. 配置和文档文件

#### `config-example.rsc`
- **用途**: 配置示例和说明
- **内容**:
  - 详细的配置参数说明
  - 常见配置示例
  - 配置验证提示
- **适用场景**: 了解配置选项和最佳实践
- **使用方法**: 参考示例修改主脚本配置

#### `README.md`
- **用途**: 完整的项目文档
- **内容**:
  - 功能特性介绍
  - 详细安装步骤
  - 配置说明
  - 故障排除指南
  - 管理命令参考
- **适用场景**: 项目的主要文档
- **使用方法**: 阅读了解项目详情

#### `项目文件说明.md`
- **用途**: 本文件，项目文件结构说明
- **内容**: 各文件的用途和使用方法
- **适用场景**: 快速了解项目结构

## 使用流程建议

### 新手用户推荐流程

1. **阅读文档**
   - 先阅读 `README.md` 了解项目功能和要求
   - 查看 `config-example.rsc` 了解配置选项

2. **获取API Token**
   - 按照README中的步骤获取Cloudflare API Token

3. **使用安装向导**
   - 运行 `install.rsc` 进行交互式安装
   - 按提示输入配置信息

4. **测试和验证**
   - 检查脚本运行状态
   - 查看系统日志
   - 验证DNS记录更新

5. **故障排除**
   - 如遇问题，运行 `troubleshoot.rsc` 进行诊断

### 有经验用户推荐流程

1. **快速部署**
   - 直接修改 `quick-setup.rsc` 顶部的配置参数
   - 运行脚本完成快速安装

2. **自定义配置**
   - 如需高级功能，直接修改 `cloudflare-ddns.rsc`
   - 手动创建定时任务

3. **维护管理**
   - 使用 `troubleshoot.rsc` 进行定期检查
   - 根据需要调整配置参数

## 文件依赖关系

```
cloudflare-ddns.rsc (主脚本)
├── config-example.rsc (配置参考)
├── quick-setup.rsc (快速安装，生成主脚本)
├── install.rsc (交互安装，生成主脚本)
└── troubleshoot.rsc (诊断工具，检查主脚本)

README.md (主文档)
└── 项目文件说明.md (本文件)
```

## 选择合适的安装方式

### 使用 `install.rsc` 如果您：
- 是MikroTik新手
- 希望有引导式的安装过程
- 需要系统兼容性检查
- 想要交互式配置

### 使用 `quick-setup.rsc` 如果您：
- 有MikroTik使用经验
- 希望快速部署
- 了解所需的配置参数
- 不需要交互式引导

### 直接使用 `cloudflare-ddns.rsc` 如果您：
- 需要完全自定义配置
- 要集成到现有脚本中
- 需要修改脚本逻辑
- 有特殊的部署需求

## 维护建议

1. **定期检查**
   - 每月运行一次 `troubleshoot.rsc` 检查系统状态
   - 定期查看系统日志确认脚本正常运行

2. **配置备份**
   - 保存脚本配置的备份
   - 记录API Token和域名配置

3. **更新管理**
   - 关注Cloudflare API变化
   - 定期更新脚本到最新版本

4. **安全考虑**
   - 定期更换API Token
   - 监控API使用情况
   - 确保MikroTik设备安全

## 技术支持

如果您在使用过程中遇到问题：

1. 首先运行 `troubleshoot.rsc` 进行自动诊断
2. 查看 `README.md` 中的故障排除部分
3. 检查系统日志获取详细错误信息
4. 参考 `config-example.rsc` 验证配置正确性

## 版本信息

- **当前版本**: 2.0
- **兼容性**: RouterOS 7.x 及以上
- **API版本**: Cloudflare API v4
- **最后更新**: 2024年

---

**注意**: 请确保在生产环境使用前充分测试所有功能，并保持配置和脚本的备份。
