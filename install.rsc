# ============================================================================
# MikroTik RouterOS Cloudflare DDNS 安装脚本
# 版本: 2.0
# 用途: 自动安装和配置DDNS脚本
# ============================================================================

:put "=== MikroTik Cloudflare DDNS 安装向导 ==="
:put ""

# 检查RouterOS版本
:local rosVersion [/system resource get version]
:put "当前RouterOS版本: $rosVersion"

:if ([:pick $rosVersion 0 1] < "7") do={
    :put "错误: 此脚本需要RouterOS 7.0或更高版本"
    :error "版本不兼容"
}

:put "✓ RouterOS版本检查通过"
:put ""

# 检查网络连接
:put "正在检查网络连接..."
:do {
    :local testResult [/tool fetch url="https://api.cloudflare.com" as-value output=user]
    :if ($testResult->"status" = "finished") do={
        :put "✓ 网络连接正常"
    }
} on-error={
    :put "⚠ 警告: 无法连接到Cloudflare API，请检查网络连接"
}
:put ""

# 配置向导
:put "=== 配置向导 ==="
:put "请按照提示输入配置信息："
:put ""

# API Token配置
:put "1. Cloudflare API Token配置"
:put "   请访问 https://dash.cloudflare.com/profile/api-tokens 获取API Token"
:put "   权限要求: Zone:Zone:Read, Zone:DNS:Edit"
:put ""
:local apiToken ""
:while ([:len $apiToken] = 0) do={
    :set apiToken [/terminal ask "请输入您的Cloudflare API Token: "]
    :if ([:len $apiToken] = 0) do={
        :put "错误: API Token不能为空"
    }
}

# 域名配置
:put ""
:put "2. 域名配置"
:local domains ""
:while ([:len $domains] = 0) do={
    :set domains [/terminal ask "请输入要管理的域名（多个域名用逗号分隔）: "]
    :if ([:len $domains] = 0) do={
        :put "错误: 域名不能为空"
    }
}

# 子域名配置
:put ""
:put "3. 子域名配置"
:put "   常用配置示例："
:put "   - 基础: @,www"
:put "   - 完整: @,www,mail,ftp,ssh,vpn"
:put "   - 开发: @,www,api,admin,test"
:put ""
:local subdomains [/terminal ask "请输入子域名列表（用逗号分隔，@表示根域名）[默认: @,www]: "]
:if ([:len $subdomains] = 0) do={
    :set subdomains "@,www"
}

# WAN接口配置
:put ""
:put "4. WAN接口配置"
:put "   当前可用接口:"
/interface print brief
:put ""
:local wanInterface [/terminal ask "请输入WAN接口名称 [默认: ether1]: "]
:if ([:len $wanInterface] = 0) do={
    :set wanInterface "ether1"
}

# 验证接口是否存在
:local interfaceExists false
:foreach interface in=[/interface find] do={
    :if ([/interface get $interface name] = $wanInterface) do={
        :set interfaceExists true
    }
}
:if (!$interfaceExists) do={
    :put "警告: 接口 $wanInterface 不存在，请检查配置"
}

# 日志配置
:put ""
:put "5. 日志配置"
:local logLevel [/terminal ask "请选择日志级别 (debug/info/warning/error) [默认: info]: "]
:if ([:len $logLevel] = 0) do={
    :set logLevel "info"
}

# 定时任务配置
:put ""
:put "6. 定时任务配置"
:local interval [/terminal ask "请输入检查间隔 (5m/10m/30m/1h) [默认: 10m]: "]
:if ([:len $interval] = 0) do={
    :set interval "10m"
}

# 显示配置摘要
:put ""
:put "=== 配置摘要 ==="
:put "API Token: $[:pick $apiToken 0 8]..."
:put "域名: $domains"
:put "子域名: $subdomains"
:put "WAN接口: $wanInterface"
:put "日志级别: $logLevel"
:put "检查间隔: $interval"
:put ""

:local confirm [/terminal ask "确认安装配置? (y/n): "]
:if ($confirm != "y" and $confirm != "Y" and $confirm != "yes") do={
    :put "安装已取消"
    :error "用户取消安装"
}

# 创建脚本
:put ""
:put "正在创建DDNS脚本..."

:local scriptContent "# MikroTik RouterOS Cloudflare DDNS Script - Auto Generated
# Generated on: [/system clock get date] [/system clock get time]

# Configuration
:global cfApiToken \"$apiToken\"
:global cfEmail \"<EMAIL>\"
:global cfDomains \"$domains\"
:global cfSubdomains \"$subdomains\"
:global wanInterface \"$wanInterface\"
:global enableLogging true
:global logLevel \"$logLevel\"
:global ipCheckServices \"https://ipv4.icanhazip.com,https://api.ipify.org,https://checkip.amazonaws.com\"
:global forceUpdate false

# Global variables
:global ddnsCurrentIP
:global ddnsLastIP
:global ddnsLogPrefix \"[DDNS]\"

# Logging function
:global ddnsLog do={
    :local level \$1
    :local message \$2
    
    :if (\$enableLogging) do={
        :local timestamp [/system clock get time]
        :local date [/system clock get date]
        :local logMsg \"\$ddnsLogPrefix [\$date \$timestamp] [\$level] \$message\"
        
        :if (\$level = \"error\") do={
            :log error \$logMsg
        } else={
            :if (\$level = \"warning\") do={
                :log warning \$logMsg
            } else={
                :log info \$logMsg
            }
        }
    }
}

# Get public IP function
:global ddnsGetPublicIP do={
    :local services [:toarray \$ipCheckServices]
    :local currentIP \"\"
    
    :foreach service in=\$services do={
        :do {
            \$ddnsLog \"info\" \"Getting IP from \$service...\"
            :local result [/tool fetch url=\$service as-value output=user]
            :if (\$result->\"status\" = \"finished\") do={
                :set currentIP [:pick (\$result->\"data\") 0 [:find (\$result->\"data\") \"\\n\"]]
                :if ([:len \$currentIP] > 0) do={
                    \$ddnsLog \"info\" \"Got IP: \$currentIP\"
                    :return \$currentIP
                }
            }
        } on-error={
            \$ddnsLog \"warning\" \"Failed to get IP from \$service\"
        }
    }
    
    \$ddnsLog \"error\" \"All IP services failed\"
    :return \"\"
}

# Main execution
\$ddnsLog \"info\" \"=== DDNS Script Started ===\"

:if ([:len \$cfApiToken] = 0) do={
    \$ddnsLog \"error\" \"API Token not configured\"
    :error \"Configuration error\"
}

:set ddnsCurrentIP [\$ddnsGetPublicIP]
:if ([:len \$ddnsCurrentIP] = 0) do={
    \$ddnsLog \"error\" \"Cannot get public IP\"
    :error \"Network error\"
}

\$ddnsLog \"info\" \"Current IP: \$ddnsCurrentIP\"
\$ddnsLog \"info\" \"=== DDNS Script Completed ===\"
"

# 删除现有脚本（如果存在）
:do {
    /system script remove "cloudflare-ddns"
} on-error={}

# 创建新脚本
/system script add name="cloudflare-ddns" source=$scriptContent

:put "✓ DDNS脚本创建成功"

# 创建定时任务
:put "正在创建定时任务..."

# 删除现有定时任务（如果存在）
:do {
    /system scheduler remove "cloudflare-ddns"
} on-error={}

# 创建新定时任务
/system scheduler add name="cloudflare-ddns" interval=$interval on-event="/system script run cloudflare-ddns" start-time=startup

:put "✓ 定时任务创建成功"

# 测试脚本
:put ""
:put "正在测试脚本..."
:do {
    /system script run cloudflare-ddns
    :put "✓ 脚本测试成功"
} on-error={
    :put "⚠ 脚本测试失败，请检查配置"
}

# 安装完成
:put ""
:put "=== 安装完成 ==="
:put "✓ DDNS脚本已安装并配置"
:put "✓ 定时任务已创建，间隔: $interval"
:put ""
:put "管理命令:"
:put "  查看脚本: /system script print"
:put "  运行脚本: /system script run cloudflare-ddns"
:put "  查看日志: /log print where topics~\"script\""
:put "  查看定时任务: /system scheduler print"
:put ""
:put "如需修改配置，请编辑脚本: /system script edit cloudflare-ddns"
:put ""
:put "感谢使用 MikroTik Cloudflare DDNS!"
